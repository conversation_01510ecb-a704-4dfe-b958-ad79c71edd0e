services:
  audio-agent:
    build: .
    container_name: audio_agent
    ports:
      - "8000:8000"
    environment:
      - WS_HOST=0.0.0.0
      - WS_PORT=8000
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
