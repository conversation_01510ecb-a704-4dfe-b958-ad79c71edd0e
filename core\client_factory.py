"""
AI客户端工厂for Audio Agent系统
统一创建和管理不同AI服务的客户端实例

按照PRD要求的API分工：
- 千问 qwen-omni-turbo-realtime (Listener + Speaker)
- 豆包 doubao-1-5-lite-32k-250115 (Tactical Thinker + Decider)  
- 豆包 doubao-1-5-lite-32k-250115 (Strategic Thinker)
"""

import logging
from typing import Any, Dict, Optional, Union
from .config import get_doubao_config, get_realtime_config
from .exceptions import APIClientError, ConfigurationError
from .retry_utils import retry_with_backoff

logger = logging.getLogger(__name__)


class ClientFactory:
    """AI客户端工厂类"""
    
    _doubao_client: Optional[Any] = None
    _qwen_realtime_client: Optional[Any] = None
    
    @classmethod
    @retry_with_backoff(max_retries=3, exceptions=(ImportError, APIClientError))
    async def get_doubao_client(cls) -> Any:
        """
        获取字节跳动豆包异步客户端 (用于Tactical Thinker、Strategic Thinker和Decider)
        
        Returns:
            AsyncOpenAI: 豆包兼容的OpenAI异步客户端实例
            
        Raises:
            APIClientError: 客户端创建失败
            ConfigurationError: 配置错误
        """
        if cls._doubao_client is None:
            try:
                from openai import AsyncOpenAI
                
                config = get_doubao_config()
                if not config.get("api_key"):
                    raise ConfigurationError("字节跳动豆包 API key is not configured")
                
                # 创建豆包客户端，使用OpenAI兼容接口
                cls._doubao_client = AsyncOpenAI(
                    api_key=config["api_key"],
                    base_url=config["base_url"]
                )
                
                logger.info("字节跳动豆包 client created successfully")
                
            except ImportError as e:
                raise APIClientError(f"Failed to import OpenAI library: {e}")
            except Exception as e:
                raise APIClientError(f"Failed to create 字节跳动豆包 client: {e}")
        
        return cls._doubao_client
    
    @classmethod
    @retry_with_backoff(max_retries=3, exceptions=(ImportError, APIClientError))
    async def get_qwen_realtime_client(cls) -> Any:
        """
        获取阿里千问qwen-omni-turbo-realtime客户端 (用于Listener和Speaker)
        
        Returns:
            AsyncOpenAI: 千问兼容的OpenAI客户端实例
            
        Raises:
            APIClientError: 客户端创建失败
            ConfigurationError: 配置错误
        """
        if cls._qwen_realtime_client is None:
            try:
                from openai import AsyncOpenAI
                
                config = get_realtime_config()
                if not config.get("api_key"):
                    raise ConfigurationError("阿里千问 API key is not configured")
                
                # 创建兼容OpenAI的千问客户端
                cls._qwen_realtime_client = AsyncOpenAI(
                    api_key=config["api_key"],
                    base_url=config["base_url"]
                )
                
                logger.info("阿里千问qwen-omni-turbo-realtime client created successfully")
                
            except ImportError as e:
                raise APIClientError(f"Failed to import OpenAI library: {e}")
            except Exception as e:
                raise APIClientError(f"Failed to create 阿里千问qwen-omni-turbo-realtime client: {e}")
        
        return cls._qwen_realtime_client
    
    # 保持向后兼容的方法名
    @classmethod
    async def get_openai_client(cls) -> Any:
        """
        向后兼容方法：返回豆包客户端
        """
        return await cls.get_doubao_client()
    
    @classmethod
    async def get_gemini_client(cls) -> Any:
        """
        向后兼容方法：返回豆包客户端
        """
        return await cls.get_doubao_client()
    
    @classmethod
    async def close_all_clients(cls) -> None:
        """
        关闭所有客户端连接
        """
        try:
            # 关闭豆包客户端
            if cls._doubao_client:
                if hasattr(cls._doubao_client, 'close'):
                    await cls._doubao_client.close()
                cls._doubao_client = None
                
            # 关闭千问客户端
            if cls._qwen_realtime_client:
                if hasattr(cls._qwen_realtime_client, 'close'):
                    await cls._qwen_realtime_client.close()
                cls._qwen_realtime_client = None
                
            logger.info("All AI clients closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing clients: {e}")
    
    @classmethod
    def clear_clients(cls) -> None:
        """
        清理所有客户端实例 (同步版本)
        """
        cls._doubao_client = None
        cls._qwen_realtime_client = None
        logger.info("All AI clients cleared")
    
    @classmethod
    async def health_check(cls) -> Dict[str, bool]:
        """
        检查所有客户端的健康状态
        
        Returns:
            Dict[str, bool]: 客户端健康状态字典
        """
        health_status = {}
        
        try:
            # 检查豆包客户端
            doubao_client = await cls.get_doubao_client()
            if doubao_client:
                health_status["doubao"] = True
            else:
                health_status["doubao"] = False
        except Exception as e:
            logger.error(f"豆包客户端健康检查失败: {e}")
            health_status["doubao"] = False
        
        try:
            # 检查千问客户端
            qwen_client = await cls.get_qwen_realtime_client()
            if qwen_client:
                health_status["qwen_realtime"] = True
            else:
                health_status["qwen_realtime"] = False
        except Exception as e:
            logger.error(f"千问Realtime客户端健康检查失败: {e}")
            health_status["qwen_realtime"] = False
        
        return health_status


# 向后兼容的函数
async def create_openai_client() -> Any:
    """向后兼容函数：创建豆包客户端"""
    return await ClientFactory.get_doubao_client()

async def create_qwen_realtime_client() -> Any:
    """向后兼容函数：创建千问Realtime客户端"""
    return await ClientFactory.get_qwen_realtime_client()

async def create_gemini_client() -> Any:
    """向后兼容函数：创建豆包客户端"""
    return await ClientFactory.get_doubao_client()


class ClientManager:
    """上下文管理器用于自动管理客户端生命周期"""
    
    def __init__(self):
        self.factory = ClientFactory
    
    async def __aenter__(self):
        return self.factory
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.factory.close_all_clients() 