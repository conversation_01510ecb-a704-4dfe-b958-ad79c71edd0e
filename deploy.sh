#!/bin/bash

# Audio Agent 部署脚本
# 主动式实时AI对话助手快速部署

set -e

echo "🚀 Audio Agent 部署脚本"
echo "=========================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    echo "   macOS: brew install docker"
    echo "   Ubuntu: sudo apt-get install docker.io"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "✅ Docker 环境检查通过"

# 检查.env文件
if [ ! -f .env ]; then
    echo "⚠️  .env 文件不存在，正在从示例文件创建..."
    cp config.env.example .env
    echo "📝 请编辑 .env 文件设置您的 API 配置"
    echo "   文件位置: $(pwd)/.env"
    echo "   🔑 需要配置两个API密钥:"
    echo "      - 阿里千问 (Listener + Speaker): QWEN_API_KEY"
    echo "      - 字节跳动豆包 (Tactical Thinker + Strategic Thinker + Decider): DOUBAO_API_KEY"
    echo "   ⚠️  确保使用正确模型: QWEN_REALTIME_MODEL=qwen-omni-turbo-realtime"
    echo "   ⚠️  豆包模型配置:"
    echo "       TACTICAL_THINKER_MODEL=doubao-1-5-lite-32k-250115"
    echo "       STRATEGIC_THINKER_MODEL=doubao-1-5-lite-32k-250115"
    echo "       DECIDER_MODEL=doubao-1-5-lite-32k-250115"
    echo "   📋 API密钥获取方式:"
    echo "       阿里千问: https://dashscope.aliyuncs.com/"
    echo "       豆包: https://console.volcengine.com/ark/"
    echo "   ⚠️  检查API权限: https://help.aliyun.com/zh/model-studio/realtime"
    
    # 询问是否现在编辑
    read -p "是否现在编辑 .env 文件? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        ${EDITOR:-nano} .env
    fi
fi

echo "✅ 环境配置检查完成"

# 创建日志目录
mkdir -p logs
echo "✅ 日志目录已创建"

# 询问部署模式
echo
echo "请选择部署模式:"
echo "1) 生产模式 (推荐)"
echo "2) 开发模式 (支持热重载)"
read -p "请输入选择 (1 或 2): " -n 1 -r
echo

case $REPLY in
    1)
        echo "🏭 启动生产模式..."
        docker compose up -d --build
        ;;
    2)
        echo "🔧 启动开发模式..."
        docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build
        ;;
    *)
        echo "❌ 无效选择，默认使用生产模式"
        docker compose up -d --build
        ;;
esac

echo
echo "⏳ 等待服务启动..."

# 健康检查重试逻辑
max_attempts=30
attempt=1
echo "🔍 开始健康检查（最多等待 ${max_attempts} 次，每次间隔 5 秒）..."

while [ $attempt -le $max_attempts ]; do
    echo "   尝试 $attempt/$max_attempts ..."
    
    if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ 服务启动成功!"
        echo
        echo "📡 访问地址:"
        echo "   - 健康检查: http://localhost:8000/health"
        echo "   - 系统状态: http://localhost:8000/status"
        echo "   - WebSocket: ws://localhost:8000/ws/conversation"
        echo
        echo "🧪 运行测试:"
        echo "   pip install aiohttp && python test_client.py"
        echo
        echo "📊 查看日志:"
        echo "   docker compose logs -f"
        echo
        echo "🛑 停止服务:"
        echo "   docker compose down"
        
        # 成功后退出
        break
    fi
    
    # 如果是最后一次尝试
    if [ $attempt -eq $max_attempts ]; then
        echo "❌ 服务启动失败，已尝试 $max_attempts 次"
        echo "📋 容器状态:"
        docker compose ps
        echo
        echo "📊 最近日志:"
        docker compose logs --tail=20
        echo
        echo "💡 可能的解决方案:"
        echo "   1. 检查详细日志: docker compose logs"
        echo "   2. 检查.env配置是否正确"
        echo "   3. 确认豆包API密钥配置"
        echo "   4. 验证阿里千问API权限"
        echo "   5. 手动测试健康检查: curl http://localhost:8000/health"
        echo "   6. 验证容器网络连接"
        exit 1
    fi
    
    sleep 5
    attempt=$((attempt + 1))
done

echo
echo "🎉 部署完成! (统一豆包架构 + 千问语音)" 