version: "3.8"

# 开发环境 Docker Compose 配置
# 使用方法: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  audio-agent:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      # 挂载源代码以支持热重载
      - ./core:/app/core
      - ./agents:/app/agents
      - ./app.py:/app/app.py
      - ./start.py:/app/start.py
      - ./test_client.py:/app/test_client.py
      # 挂载配置文件
      - ./.env:/app/.env
      - ./config.env.example:/app/config.env.example
      # 挂载日志目录
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=DEBUG
      # 开发模式特定配置
      - RELOAD=true
    ports:
      - "8000:8000"
      - "6379:6379" # Redis端口，用于外部连接调试
    command: >
      bash -c "
        echo '🔧 Development Mode - Hot Reload Enabled' &&
        python -c 'import sys; print(f\"Python: {sys.version}\")' &&
        pip install watchdog &&
        uvicorn app:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app
      "
    restart: unless-stopped

  # 可选：独立的Redis服务用于开发调试
  redis-dev:
    image: redis:8.0-alpine
    container_name: audio_agent_redis_dev
    ports:
      - "6380:6379" # 避免与主Redis冲突
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes --loglevel verbose
    restart: unless-stopped

volumes:
  redis_dev_data:
