# 主动式实时 AI 对话助手 (Proactive AI Conversation Assistant)

> 🚀 基于 Dispider 架构的先进实时语音对话系统，具备 AI 主动打断能力

## 📖 项目概述

本项目是一款先进的实时 AI 语音对话助手，实现了**主动式对话管理**的突破性功能。与传统的被动式助手不同，我们的系统能够智能地主动打断用户，提供更自然、高效的对话体验。

### 🎯 核心特性

  - **🎤 实时语音交互**: 基于阿里千问 qwen-omni-turbo-realtime API 的端到端语音处理
  - **🧠 主动打断能力**: AI 能够智能判断并主动打断用户，引导对话方向
  - **⚡ 极低延迟**: 端到端延迟控制在 500ms 以内
  - **🎯 双模式支持**: 标准助手模式 + AI 面试官模式
  - **🌐 多语言支持**: 流畅的中英文对话
  - **🔄 实时响应**: 边听边想的并发处理架构

### 🏗️ Dispider 架构

本系统严格遵循 **Dispider 原则**，将实时交互解耦为三个核心层：

```
📥 感知层 (Perception)    → 🧠 决策层 (Decision)      → 📤 反应层 (Reaction)
    Listener                  Tactical + Strategic         Speaker
                               + Decider
```

#### 🎯 统一豆包架构

| 组件 | API | 模型 | 职责 |
| :--- | :--- | :--- | :--- |
| **Listener + Speaker** | 阿里千问 | qwen-omni-turbo-realtime | 实时语音输入输出 |
| **Tactical Thinker + Decider** | 字节跳动豆包 | doubao-1-5-lite-32k-250115 | 快速意图分析与决策 |
| **Strategic Thinker** | 字节跳动豆包 | doubao-1-5-lite-32k-250115 | 上下文分析与决策 |

## 🚀 快速开始

### 📋 前置要求

  - Python 3.9+ (推荐 3.11)
  - Node.js 16+
  - Docker (可选)
  - `uv` (可选, 一个极速的 Python 包安装和解析工具)
  - API 密钥:
      - [阿里云 DashScope API Key](https://dashscope.aliyuncs.com/)
      - [字节跳动豆包 API Key](https://console.volcengine.com/ark/)

### ⚙️ 环境配置

1.  **克隆项目**

    ```bash
    git clone <repository-url>
    cd audio_agent
    ```

2.  **配置环境变量**

    ```bash
    cp config.env.example .env
    ```

    编辑 `.env` 文件，填入你的 API 密钥：

    ```env
    # 阿里千问配置 (Listener + Speaker)
    QWEN_API_KEY=your_qwen_api_key_here
    QWEN_REALTIME_MODEL=qwen-omni-turbo-realtime
    QWEN_WS_ENDPOINT=wss://dashscope.aliyuncs.com/api-ws/v1/realtime

    # 字节跳动豆包 配置 (Tactical Thinker + Strategic Thinker + Decider)
    DOUBAO_API_KEY=your_doubao_api_key_here

    # 系统配置
    WS_HOST=0.0.0.0
    WS_PORT=8000
    LOG_LEVEL=INFO
    ```

### 🐳 Docker 部署 (推荐)

**一键启动**:

```bash
./deploy.sh
```

这将：

  - 构建 Docker 镜像
  - 启动后端服务 (端口 8000)
  - 生成详细的启动日志

**查看日志**:

```bash
# 实时日志
docker-compose logs -f

# 错误调试日志 (非常有用!)
tail -f logs/audio_agent_error.log
```

### 💻 本地开发部署 (不使用 Docker)

**后端启动**:

1.  **创建并激活虚拟环境 (使用 uv)**:

    ```bash
    # 使用 Python 3.11 创建名为 venv 的虚拟环境
    uv venv -p 3.11 venv

    # 激活虚拟环境 （如果已经创建了，则可以直接跑这行代码）
    source venv/bin/activate
    ```

2.  **安装依赖**:

    ```bash
    # 使用 uv 安装依赖，速度非常快
    uv pip install -r requirements.txt
    ```

3.  **启动服务**:

    ```bash
    # 启动后端应用
    python start.py
    ```

**查看日志**:

后端日志会输出到控制台，同时也会记录在 `logs/` 目录下。可以查看日志文件来调试：

```bash
# 实时查看错误日志
tail -f logs/audio_agent_error.log
```

*提示：重新启动服务前，可以删除旧的日志文件 (`rm logs/*.log`) 以便观察最新的日志。*

**退出虚拟环境**:

当你完成开发后，可以使用以下命令退出虚拟环境：

```bash
deactivate
```

-----

**前端启动**:

```bash
cd client
npm install
npm run dev
```

前端服务将在 `http://localhost:3000` 启动。

## 🎮 使用指南

### 📱 Web 界面操作

1.  **连接服务器**: 点击 "连接" 按钮
2.  **选择模式**:
      - **标准助手模式**: 友好的语音助手
      - **面试官模式**: AI 技术面试官 (主动打断更积极)
3.  **开始对话**: 点击 "开始录音" 或使用 `Ctrl + Space` 快捷键
4.  **体验主动打断**: AI 会在合适时机主动介入对话

### 🎯 智能打断逻辑说明

系统采用双流分析架构，两个 AI Thinker 并行工作，Decider 进行最后的分发决定：

#### 🤝 合作型打断 (Cooperative Interruption) - Tactical Thinker

  - **AI 特点**: 快速响应，专注实时分析
  - **触发场景**:
      - **自然附和**: "xxxxx" → AI: "确实是这样的"
      - **情感支持**: 用户表达困惑时主动协助 → AI: "没事的/问题不大啦"
  - **技术优势**: 基于语义理解，实现自然对话流
  - **更新**: 暂时放弃实时通过音频打断，而是只在文字层面输出emoji或者简单带方框的语言或者两者结合比如【确实是👍】，不再尝试直接生成附和型声音

#### 🛑 干扰型打断 (Disruptive Interruption) - Strategic Thinker

  - **AI 特点**: 深度分析，全局记忆管理，无时间限制
  - **触发场景**:
      - **话题控制**: 面试中偏题 → AI: "让我们回到技术问题上" (只在特定模式比如面试官模式生效)
      - **重复冗长管理**: 回答过于冗长 → AI: "请简洁一些，重点是什么？"
      - **时间管理**: 超出合理发言时长时主动引导
  - **技术优势**: 持续运行，边听边想，主动推送决策。存memory，大概20轮对话的信息量，ai assistant的转录和人的转录都要记录，作为上下文，再实时接收delta并做出相应的判断比如 重复冗长等等

#### ⚖️ 智能优先级仲裁

**Decider 纯事件驱动决策**:

1.  **Strategic Thinker (干扰型) 和 Tactical Thinker (合作型) 目前并行 因为他们处理的事件也不一样**
2.  **无超时等待** - 每个 Thinker 独立工作，主动推送决策
3.  **双阈值系统** - 合作型阈值 0.2，干扰型阈值 0.15

## 🏗️ 系统架构

### 🎯 架构优化亮点 (v2.1)

🚀 **双流分析架构**:

  - **Tactical Thinker** 专注高频快速响应，实现自然附和
  - **Strategic Thinker** 进行上下文分析，管理全局记忆

⚡ **纯事件驱动设计**:

  - 移除所有超时等待机制，每个 Thinker 主动推送决策
  - Decider 成为纯仲裁者，响应延迟大幅降低

🧠 **语义理解升级**:

  - 从关键词匹配升级为语义理解
  - 支持真人对话中的自然附和："确实是这样的"

### 🔄 数据流图

```mermaid
graph TB
    Client[客户端] <--> FastAPI[FastAPI后端]
    FastAPI <--> RM[全局RealtimeManager<br/>唯一实例]
    RM <--> API[千问API<br/>单一WebSocket连接]

    Listener[Listener] -.委托.-> RM
    Speaker[Speaker] -.委托.-> RM

    RM <--> MB[MessageBus]
    MB <--> Thinkers[Thinkers]
    MB <--> Decider[Decider]
    MB <--> Orchestrator[Orchestrator]
```

### 📦 核心组件

| 组件 | 职责 | API | 工作模式 |
| :--- | :--- | :--- | :--- |
| **Orchestrator** | 中央协调器，管理会话状态 | - | 事件驱动 |
| **Listener** | 处理用户语音输入，实时转录 | 阿里千问 | 被动监听 |
| **Tactical Thinker** | 🤝 快速语义分析，自然附和，合作型打断 | 豆包 doubao-1-5-lite-32k-250115 | **主动推送决策** |
| **Strategic Thinker**| 🧠 上下文分析，管理对话历史，干扰型打断 | 豆包 doubao-1-5-lite-32k-250115 | **持续运行分析** |
| **Decider** | ⚖️ 接收Thinker决策并进行分发 | 豆包 doubao-1-5-lite-32k-250115 | **纯事件驱动** |
| **Speaker** | AI 语音输出和合成 | 阿里千问 | 指令执行 |

## 🏗️ 技术栈选择

### 🧠 AI 模型架构

| 用途 | 模型选择 | 特点 | 优势 |
| :--- | :--- | :--- | :--- |
| **语音处理** | 阿里千问 qwen-omni-turbo-realtime | 实时语音转写+合成 | 专为中文优化，超低延迟 |
| **快速分析** | 豆包 doubao-1-5-lite-32k-250115 | 轻量高速推理 | 毫秒级响应，适合实时决策 |
| **深度分析** | 豆包 doubao-1-5-lite-32k-250115 | 上下文记忆与分析 | 支持长对话历史分析，统一模型架构 |

### 🌏 针对中国网络环境优化

  - **🚀 国内API**: 豆包和千问均为国内服务商，网络延迟更低
  - **🔧 统一架构**: 所有AI调用使用OpenAI兼容格式，代码结构统一
  - **⚡ 高可用性**: 避免海外API的网络不稳定问题
  - **💰 成本优化**: 国内API定价更有竞争力

## 🔧 配置详解

### 🎚️ 打断阈值调整

```env
# 合作型打断阈值 (越低越容易触发)
COOPERATIVE_INTERRUPTION_THRESHOLD=0.2

# 干扰型打断阈值 (越低越主动)
DISRUPTIVE_INTERRUPTION_THRESHOLD=0.15

# 静音检测时长
SILENCE_DURATION_MS=200
```

### 🎭 面试官模式配置

```env
# 启用面试官模式
INTERVIEWER_MODE=true

# 最大发言时间 (秒)
INTERVIEWER_MAX_SPEAKING_TIME=30

# 强制打断时间 (秒)
INTERVIEWER_FORCE_INTERRUPT_AFTER=45

# 面试官模式阈值 (更激进)
INTERVIEWER_COOPERATIVE_THRESHOLD=0.2
INTERVIEWER_DISRUPTIVE_THRESHOLD=0.15
```

## 🐛 故障排除

### 📊 常见问题

#### 1\. API 连接失败

```bash
# 检查 API 密钥配置
grep -E "API_KEY" .env

# 查看连接日志
tail -f logs/audio_agent_error.log | grep -E "API|连接"
```

#### 2\. 音频无法播放

  - 确保浏览器已允许音频权限
  - 检查浏览器控制台是否有 AudioContext 错误
  - 尝试点击页面任意位置激活音频上下文

#### 3\. 打断功能不工作

```bash
# 检查 Thinker 组件状态
curl http://localhost:8000/health | jq '.system.agents'

# 查看打断决策日志 (新架构)
tail -f logs/audio_agent_error.log | grep -E "🤝|🛑|⚖️|打断|interruption"

# 监控 Thinker 性能
tail -f logs/audio_agent_error.log | grep -E "Strategic Thinker|Tactical Thinker|深度分析|附和"

# 检查事件驱动消息流
tail -f logs/audio_agent_error.log | grep -E "COOPERATIVE_ANALYSIS|DISRUPTIVE_ANALYSIS"
```

#### 4\. 前端连接失败

```bash
# 确保后端服务运行
curl http://localhost:8000/health

# 检查 WebSocket 连接
# 浏览器开发者工具 → Network → WS
```

### 🔍 调试技巧

1.  **启用详细日志**:

    ```env
    LOG_LEVEL=DEBUG
    ```

2.  **监控系统状态**:

    ```bash
    # 健康检查
    curl http://localhost:8000/health

    # 系统状态
    curl http://localhost:8000/status
    ```

3.  **音频调试**:

      - 前端界面中启用 "调试日志"
      - 观察音频数据流和转录结果

## 📚 API 参考

### 🔌 WebSocket 端点

```javascript
// 连接
ws://localhost:8000/ws/conversation

// 消息格式
{
  "type": "mode_selection",
  "mode": "standard" | "interviewer"
}

{
  "type": "audio_input",
  "data": "base64_pcm_data"
}
```

### 🌐 HTTP 端点

| 端点 | 方法 | 描述 |
| :--- | :--- | :--- |
| `/health` | GET | 系统健康检查 |
| `/status` | GET | 详细系统状态 |
| `/api/sessions` | GET | 活跃会话列表 |

## 🤝 开发贡献

### 🔨 开发环境搭建

```bash
# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt

# 启动开发模式
python app.py

# 前端开发
cd client && npm run dev
```

### 🧪 测试

```bash
# 运行测试
python -m pytest tests/

# API 连接测试
python -m pytest tests/test_api_connections.py

# 端到端测试
python -m pytest tests/test_e2e.py
```

## 📄 许可证

MIT License - 详见 [LICENSE](https://www.google.com/search?q=LICENSE) 文件

## 🙏 致谢

  - [阿里千问](https://tongyi.aliyun.com/) - 实时语音 API
  - [字节跳动豆包](https://www.volcengine.com/product/doubao) - AI推理和分析模型

-----

🌟 **Star this project if you find it useful\!**

📧 **Questions?** Open an issue or contact the development team.

**享受主动式 AI 对话的全新体验！** 🚀