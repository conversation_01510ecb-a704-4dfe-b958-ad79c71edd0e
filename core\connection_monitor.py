"""
连接监控和自动恢复模块

专门为音频流场景设计的WebSocket连接监控系统，提供：
1. 实时连接健康监控
2. 智能自动恢复机制
3. 音频流质量监控
4. 网络质量评估
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
from loguru import logger
from dataclasses import dataclass
from enum import Enum


class ConnectionHealth(Enum):
    """连接健康状态"""
    EXCELLENT = "excellent"    # 连接优秀
    GOOD = "good"             # 连接良好
    FAIR = "fair"             # 连接一般
    POOR = "poor"             # 连接较差
    CRITICAL = "critical"     # 连接临界
    DISCONNECTED = "disconnected"  # 已断开


@dataclass
class ConnectionMetrics:
    """连接质量指标"""
    ping_time: float = 0.0           # ping延迟(ms)
    packet_loss: float = 0.0         # 丢包率(%)
    audio_delay: float = 0.0         # 音频延迟(ms)
    bandwidth_usage: float = 0.0     # 带宽使用(KB/s)
    error_rate: float = 0.0          # 错误率(%)
    last_update: datetime = None
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()


class ConnectionMonitor:
    """连接监控器"""
    
    def __init__(self, realtime_manager, check_interval: float = 10.0):
        self.realtime_manager = realtime_manager
        self.check_interval = check_interval
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # 连接质量指标
        self.metrics = ConnectionMetrics()
        self.health_status = ConnectionHealth.DISCONNECTED
        
        # 历史数据（用于趋势分析）
        self.ping_history = []
        self.error_history = []
        self.max_history_length = 50
        
        # 回调函数
        self.health_change_callback: Optional[Callable] = None
        self.recovery_callback: Optional[Callable] = None
        
        # 自动恢复配置
        self.auto_recovery_enabled = True
        self.recovery_threshold = ConnectionHealth.POOR
        self.max_recovery_attempts = 3
        self.recovery_cooldown = 30.0  # 恢复冷却时间(秒)
        
        # 内部状态
        self.last_recovery_time = None
        self.recovery_attempts = 0
        
    async def start_monitoring(self) -> bool:
        """开始连接监控"""
        if self.is_monitoring:
            logger.warning("Connection monitor already running")
            return True
            
        try:
            self.is_monitoring = True
            self.monitor_task = asyncio.create_task(self._monitoring_loop())
            logger.info("Connection monitor started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start connection monitor: {e}")
            self.is_monitoring = False
            return False
    
    async def stop_monitoring(self):
        """停止连接监控"""
        self.is_monitoring = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            self.monitor_task = None
            
        logger.info("Connection monitor stopped")
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_monitoring:
                await self._check_connection_health()
                await asyncio.sleep(self.check_interval)
                
        except asyncio.CancelledError:
            logger.info("Connection monitoring cancelled")
        except Exception as e:
            logger.error(f"Error in connection monitoring loop: {e}")
            
    async def _check_connection_health(self):
        """检查连接健康状态"""
        try:
            # 更新连接指标
            await self._update_metrics()
            
            # 评估健康状态
            new_health = self._evaluate_health()
            
            # 如果健康状态发生变化，触发回调
            if new_health != self.health_status:
                old_status = self.health_status
                self.health_status = new_health
                
                logger.info(f"Connection health changed: {old_status.value} -> {new_health.value}")
                
                if self.health_change_callback:
                    try:
                        await self.health_change_callback(old_status, new_health, self.metrics)
                    except Exception as e:
                        logger.error(f"Error in health change callback: {e}")
            
            # 检查是否需要自动恢复
            if self.auto_recovery_enabled:
                await self._check_auto_recovery()
                
        except Exception as e:
            logger.error(f"Error checking connection health: {e}")
            
    async def _update_metrics(self):
        """更新连接指标"""
        current_time = time.time()
        
        # 检查WebSocket连接状态
        if not self.realtime_manager.is_connection_healthy():
            self.metrics.ping_time = float('inf')
            self.metrics.packet_loss = 100.0
            return
            
        # 测量ping延迟
        try:
            ping_start = time.time()
            # 发送简单的ping测试（如果API支持）
            # 这里使用连接健康检查作为ping替代
            is_healthy = self.realtime_manager.is_connection_healthy()
            ping_end = time.time()
            
            if is_healthy:
                ping_time = (ping_end - ping_start) * 1000  # 转换为毫秒
                self.metrics.ping_time = ping_time
                
                # 更新ping历史
                self.ping_history.append(ping_time)
                if len(self.ping_history) > self.max_history_length:
                    self.ping_history.pop(0)
            else:
                self.metrics.ping_time = float('inf')
                
        except Exception as e:
            logger.debug(f"Error measuring ping: {e}")
            self.metrics.ping_time = float('inf')
            
        # 计算丢包率（基于ping历史）
        if self.ping_history:
            failed_pings = sum(1 for p in self.ping_history if p == float('inf'))
            self.metrics.packet_loss = (failed_pings / len(self.ping_history)) * 100
        
        # 更新时间戳
        self.metrics.last_update = datetime.now()
        
    def _evaluate_health(self) -> ConnectionHealth:
        """评估连接健康状态"""
        # 如果连接已断开
        if not self.realtime_manager.is_connection_healthy():
            return ConnectionHealth.DISCONNECTED
            
        # 基于ping延迟评估
        ping = self.metrics.ping_time
        loss = self.metrics.packet_loss
        
        if ping == float('inf') or loss >= 50:
            return ConnectionHealth.CRITICAL
        elif ping > 1000 or loss >= 20:
            return ConnectionHealth.POOR
        elif ping > 500 or loss >= 10:
            return ConnectionHealth.FAIR
        elif ping > 200 or loss >= 5:
            return ConnectionHealth.GOOD
        else:
            return ConnectionHealth.EXCELLENT
            
    async def _check_auto_recovery(self):
        """检查是否需要自动恢复"""
        # 如果健康状态好于恢复阈值，重置恢复计数
        if self.health_status.value not in [ConnectionHealth.POOR.value, 
                                           ConnectionHealth.CRITICAL.value, 
                                           ConnectionHealth.DISCONNECTED.value]:
            self.recovery_attempts = 0
            return
            
        # 检查是否在冷却期内
        if self.last_recovery_time:
            time_since_recovery = time.time() - self.last_recovery_time
            if time_since_recovery < self.recovery_cooldown:
                return
                
        # 检查是否超过最大恢复尝试次数
        if self.recovery_attempts >= self.max_recovery_attempts:
            logger.warning(f"Max recovery attempts ({self.max_recovery_attempts}) reached")
            return
            
        # 执行自动恢复
        await self._execute_recovery()
        
    async def _execute_recovery(self):
        """执行连接恢复"""
        try:
            self.recovery_attempts += 1
            self.last_recovery_time = time.time()
            
            logger.info(f"Executing connection recovery (attempt {self.recovery_attempts})")
            
            # 尝试重连
            success = await self.realtime_manager.reconnect_with_backoff(max_retries=2)
            
            if success:
                logger.info("Connection recovery successful")
                self.recovery_attempts = 0  # 重置计数
                
                if self.recovery_callback:
                    try:
                        await self.recovery_callback(True, self.recovery_attempts)
                    except Exception as e:
                        logger.error(f"Error in recovery callback: {e}")
            else:
                logger.warning(f"Connection recovery failed (attempt {self.recovery_attempts})")
                
                if self.recovery_callback:
                    try:
                        await self.recovery_callback(False, self.recovery_attempts)
                    except Exception as e:
                        logger.error(f"Error in recovery callback: {e}")
                        
        except Exception as e:
            logger.error(f"Error during connection recovery: {e}")
            
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康状态报告"""
        avg_ping = 0.0
        if self.ping_history:
            valid_pings = [p for p in self.ping_history if p != float('inf')]
            avg_ping = sum(valid_pings) / len(valid_pings) if valid_pings else 0.0
            
        return {
            "health_status": self.health_status.value,
            "current_metrics": {
                "ping_time": self.metrics.ping_time,
                "packet_loss": self.metrics.packet_loss,
                "audio_delay": self.metrics.audio_delay,
                "bandwidth_usage": self.metrics.bandwidth_usage,
                "error_rate": self.metrics.error_rate,
                "last_update": self.metrics.last_update.isoformat() if self.metrics.last_update else None
            },
            "statistics": {
                "average_ping": avg_ping,
                "ping_samples": len(self.ping_history),
                "recovery_attempts": self.recovery_attempts,
                "last_recovery": self.last_recovery_time
            },
            "config": {
                "auto_recovery_enabled": self.auto_recovery_enabled,
                "check_interval": self.check_interval,
                "recovery_threshold": self.recovery_threshold.value
            }
        }
    
    def set_health_change_callback(self, callback: Callable):
        """设置健康状态变化回调"""
        self.health_change_callback = callback
        
    def set_recovery_callback(self, callback: Callable):
        """设置恢复回调"""
        self.recovery_callback = callback 