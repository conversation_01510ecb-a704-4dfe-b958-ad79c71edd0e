"""
Event types and message definitions for the Audio Agent system.
Based on the Dispider architecture and multi-agent communication.
"""

from enum import Enum
from typing import Any, Dict, Optional
from pydantic import BaseModel
from datetime import datetime


class EventType(str, Enum):
    """
    Core event types for the Dispider architecture.
    """
    # 感知层事件 (Perception Layer)
    AUDIO_CHUNK_RECEIVED = "audio_chunk_received"
    AUDIO_OUTPUT = "audio_output"  # 音频输出事件
    TRANSCRIPT_DELTA = "transcript_delta"
    TRANSCRIPT_COMPLETED = "transcript_completed"
    SPEECH_STARTED = "speech_started"
    SPEECH_STOPPED = "speech_stopped"
    SPEECH_COMPLETED = "speech_completed"
    SPEECH_INTERRUPTED = "speech_interrupted"
    
    # 语音检测和音频控制事件
    SPEECH_DETECTION = "speech_detection"  # 语音检测事件
    SPEECH_INTERRUPTION = "speech_interruption"  # 语音中断事件
    AUDIO_PLAYBACK_START = "audio_playback_start"  # 音频播放开始
    AUDIO_PLAYBACK_STOP = "audio_playback_stop"  # 音频播放停止
    AUDIO_QUEUE_UPDATE = "audio_queue_update"  # 音频队列状态更新
    
    # 决策层事件 (Decision Layer)
    TACTICAL_ANALYSIS = "tactical_analysis"
    STRATEGIC_ANALYSIS = "strategic_analysis"
    INTERRUPTION_DECISION = "interruption_decision"
    RESPONSE_REQUIRED = "response_required"
    RESPONSE_GENERATED = "response_generated"
    
    # 反应层事件 (Reaction Layer)
    TTS_START = "tts_start"
    TTS_STOP = "tts_stop"
    TTS_AUDIO_CHUNK = "tts_audio_chunk"
    RESPONSE_COMPLETED = "response_completed"
    
    # 连接和质量事件
    CONNECTION_ESTABLISHED = "connection_established"
    CONNECTION_LOST = "connection_lost"
    CONNECTION_QUALITY_CHANGED = "connection_quality_changed"
    HEARTBEAT_PING = "heartbeat_ping"
    HEARTBEAT_PONG = "heartbeat_pong"
    
    # 系统事件
    AGENT_STARTED = "agent_started"
    AGENT_STOPPED = "agent_stopped"
    SESSION_CREATED = "session_created"
    SESSION_ENDED = "session_ended"
    ERROR_OCCURRED = "error_occurred"
    UI_UPDATE = "ui_update"  # UI更新事件，用于合作型附和
    
    # 🎯 新增：音频护盾控制事件
    LISTENER_PAUSE = "listener_pause"  # 暂停Listener音频处理
    LISTENER_RESUME = "listener_resume"  # 恢复Listener音频处理


class MessageType(str, Enum):
    """
    Message types for inter-agent communication.
    """
    # Orchestrator消息
    ORCHESTRATOR_COMMAND = "orchestrator_command"
    ORCHESTRATOR_STATUS = "orchestrator_status"
    
    # Listener消息
    LISTENER_AUDIO_DATA = "listener_audio_data"
    LISTENER_TRANSCRIPT = "listener_transcript"
    
    # Thinker消息
    TACTICAL_INTENT = "tactical_intent"
    STRATEGIC_CONTENT = "strategic_content"
    
    # Decider消息
    INTERRUPTION_SIGNAL = "interruption_signal"
    DECISION_RESULT = "decision_result"
    
    # Speaker消息
    SPEAKER_COMMAND = "speaker_command"
    SPEAKER_AUDIO = "speaker_audio"
    
    # 用户输入消息
    USER_INPUT = "user_input"


class InterruptionType(str, Enum):
    """
    Types of interruptions based on the PRD specification.
    """
    # 合作型打断 (Cooperative Interruption)
    AGREEMENT = "agreement"  # 表示同意
    ASSISTANCE = "assistance"  # 提供协助
    CLARIFICATION = "clarification"  # 请求澄清
    
    # 干扰型/话题控制型打断 (Disruptive/Topic Control Interruption)
    TOPIC_CONTROL = "topic_control"  # 话题控制
    OFF_TOPIC = "off_topic"  # 偏离主题
    INFORMATION_OVERLOAD = "information_overload"  # 信息过载


class AgentState(str, Enum):
    """
    Agent states for the multi-agent system.
    """
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    RESPONDING = "responding"
    ERROR = "error"


class BaseMessage(BaseModel):
    """
    Base message class for all inter-agent communications.
    """
    message_id: str
    timestamp: datetime
    source_agent: str
    target_agent: Optional[str] = None
    message_type: MessageType
    event_type: EventType
    data: Dict[str, Any]
    priority: int = 1  # 1=highest, 5=lowest


class AudioMessage(BaseMessage):
    """
    Message containing audio data.
    """
    audio_data: bytes
    sample_rate: int
    channels: int
    duration_ms: int


class TranscriptMessage(BaseMessage):
    """
    Message containing transcript data.
    """
    text: str
    confidence: float
    is_final: bool
    language: Optional[str] = None


class InterruptionMessage(BaseMessage):
    """
    Message for interruption decisions.
    """
    interruption_type: InterruptionType
    confidence: float
    reason: str
    suggested_response: Optional[str] = None


class OrchstratorCommand(BaseMessage):
    """
    Command message from Orchestrator.
    """
    command: str
    parameters: Dict[str, Any]
    session_id: str


class TacticalAnalysis(BaseMessage):
    """
    Tactical analysis from Tactical Thinker.
    """
    intent: str
    confidence: float
    keywords: list[str]
    sentiment: str
    urgency: int  # 1-5 scale


class StrategicAnalysis(BaseMessage):
    """
    Strategic analysis from Strategic Thinker.
    """
    content: str
    context: Dict[str, Any]
    reasoning: str
    suggested_actions: list[str] 