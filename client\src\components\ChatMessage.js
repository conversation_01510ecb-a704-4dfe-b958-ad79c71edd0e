/**
 * Chat Message Component for Audio Agent Client
 * Manages individual chat messages with real-time status updates
 */

// Message status constants
const MessageStatus = {
  TYPING: "typing", // AI正在回复
  STREAMING: "streaming", // 流式输入中
  COMPLETED: "completed", // 完成
  ERROR: "error", // 错误
  PLAYING: "playing", // 音频播放中
};

// Message types
const MessageType = {
  USER: "user",
  ASSISTANT: "assistant",
  SYSTEM: "system",
};

class ChatMessage {
  constructor(id, type, content = "", status = MessageStatus.COMPLETED) {
    this.id = id;
    this.type = type;
    this.content = content;
    this.status = status;
    // 🎯 修复：使用高精度时间戳确保消息顺序
    this.timestamp = new Date();
    this.timestampMs = Date.now() + Math.random() * 0.1; // 添加微小随机值避免完全相同的时间戳
    this.audioData = null;
    this.isAudioPlaying = false;
  }

  /**
   * Render message as HTML element
   * @returns {HTMLElement} Message element
   */
  render() {
    const messageElement = document.createElement("div");
    messageElement.className = this.getMessageClasses();
    messageElement.id = `message-${this.id}`;
    messageElement.setAttribute("data-message-id", this.id);

    const messageHTML = this.generateMessageHTML();
    messageElement.innerHTML = messageHTML;

    // Add event listeners for interactive elements
    this.attachEventListeners(messageElement);

    return messageElement;
  }

  /**
   * Generate CSS classes for message
   * @returns {string} CSS class string
   */
  getMessageClasses() {
    const baseClasses = ["chat-message", `message-${this.type}`];

    if (this.status === MessageStatus.TYPING) {
      baseClasses.push("message-typing");
    } else if (this.status === MessageStatus.STREAMING) {
      baseClasses.push("message-streaming");
    } else if (this.status === MessageStatus.PLAYING) {
      baseClasses.push("message-playing");
    } else if (this.status === MessageStatus.ERROR) {
      baseClasses.push("message-error");
    }

    return baseClasses.join(" ");
  }

  /**
   * Generate message HTML content
   * @returns {string} HTML string
   */
  generateMessageHTML() {
    const timeString = this.timestamp.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });

    const icon = this.getMessageIcon();
    const name = this.getMessageSenderName();
    const statusIndicator = this.getStatusIndicator();

    return `
            <div class="message-header">
                <span class="message-icon">${icon}</span>
                <span class="message-sender">${name}</span>
                <span class="message-time">${timeString}</span>
                ${statusIndicator}
            </div>
            <div class="message-body">
                <div class="message-content" id="content-${this.id}">
                    ${this.formatContent()}
                </div>
                ${this.renderAudioControls()}
            </div>
        `;
  }

  /**
   * Get message icon based on type
   * @returns {string} Icon emoji
   */
  getMessageIcon() {
    switch (this.type) {
      case MessageType.USER:
        return "👤";
      case MessageType.ASSISTANT:
        return "🤖";
      case MessageType.SYSTEM:
        return "⚙️";
      default:
        return "💬";
    }
  }

  /**
   * Get sender name based on type
   * @returns {string} Sender name
   */
  getMessageSenderName() {
    switch (this.type) {
      case MessageType.USER:
        return "You";
      case MessageType.ASSISTANT:
        return "AI Assistant";
      case MessageType.SYSTEM:
        return "System";
      default:
        return "Unknown";
    }
  }

  /**
   * Get status indicator HTML
   * @returns {string} Status indicator HTML
   */
  getStatusIndicator() {
    switch (this.status) {
      case MessageStatus.TYPING:
        return '<span class="status-indicator typing">正在输入...</span>';
      case MessageStatus.STREAMING:
        return '<span class="status-indicator streaming">●</span>';
      case MessageStatus.PLAYING:
        return '<span class="status-indicator playing">🔊</span>';
      case MessageStatus.ERROR:
        return '<span class="status-indicator error">❌</span>';
      default:
        return "";
    }
  }

  /**
   * Format message content based on status
   * @returns {string} Formatted content
   */
  formatContent() {
    if (
      this.status === MessageStatus.TYPING &&
      this.type === MessageType.ASSISTANT
    ) {
      return '<div class="typing-animation"><span></span><span></span><span></span></div>';
    }

    if (!this.content && this.status === MessageStatus.STREAMING) {
      return '<div class="streaming-placeholder">正在接收...</div>';
    }

    // 🎯 修复：移除流式文本中的光标，因为我们在animateStreamingText中单独处理
    let content = this.content || "";

    // 移除可能存在的光标字符
    content = content.replace(/▋$/, '');

    return content;
  }

  /**
   * Render audio controls if message has audio
   * @returns {string} Audio controls HTML
   */
  renderAudioControls() {
    if (this.type === MessageType.ASSISTANT && this.audioData) {
      return `
                <div class="audio-controls">
                    <button class="audio-play-btn" data-message-id="${this.id}">
                        ${this.isAudioPlaying ? "⏸️" : "▶️"}
                    </button>
                    <span class="audio-duration">🎵</span>
                </div>
            `;
    }
    return "";
  }

  /**
   * Attach event listeners to message element
   * @param {HTMLElement} element Message element
   */
  attachEventListeners(element) {
    // Audio play button
    const playBtn = element.querySelector(".audio-play-btn");
    if (playBtn) {
      playBtn.addEventListener("click", (e) => {
        e.preventDefault();
        this.toggleAudioPlayback();
      });
    }
  }

  /**
   * Update message content with optional streaming effect
   * @param {string} newContent New content
   * @param {string} newStatus New status
   * @param {boolean} useStreamingEffect Whether to use streaming effect
   */
  updateContent(newContent, newStatus = null, useStreamingEffect = false) {
    const oldContent = this.content;
    this.content = newContent;
    if (newStatus) {
      this.status = newStatus;
    }

    // Update DOM if message is rendered
    const messageElement = document.getElementById(`message-${this.id}`);
    if (messageElement) {
      const contentElement = messageElement.querySelector(
        `#content-${this.id}`
      );

      if (contentElement) {
        // 🎯 新增：流式文本效果
        if (useStreamingEffect &&
            this.type === MessageType.ASSISTANT &&
            this.status === MessageStatus.STREAMING &&
            newContent.length > oldContent.length) {
          this.animateStreamingText(contentElement, oldContent, newContent);
        } else {
          // 清理可能存在的流式光标
          this.clearStreamingCursor(contentElement);
          contentElement.innerHTML = this.formatContent();
        }
      }

      // Update message classes
      messageElement.className = this.getMessageClasses();

      // Update status indicator
      const headerElement = messageElement.querySelector(".message-header");
      if (headerElement) {
        const statusElement = headerElement.querySelector(".status-indicator");
        if (statusElement) {
          statusElement.remove();
        }
        const newStatusHTML = this.getStatusIndicator();
        if (newStatusHTML) {
          headerElement.insertAdjacentHTML("beforeend", newStatusHTML);
        }
      }
    }
  }

  /**
   * Animate streaming text with typewriter effect
   * @param {HTMLElement} contentElement Content element
   * @param {string} oldContent Previous content
   * @param {string} newContent New content
   */
  animateStreamingText(contentElement, oldContent, newContent) {
    console.log(`🎯 animateStreamingText: oldContent="${oldContent}", newContent="${newContent}"`);

    // 特殊处理：如果旧内容为空且新内容不为空，这是第一次显示
    if (!oldContent && newContent) {
      console.log(`🎯 First time display, starting typewriter effect`);
      this.startTypewriterEffect(contentElement, newContent);
      return;
    }

    // 如果新内容比旧内容短，直接更新（可能是重置）
    if (newContent.length <= oldContent.length) {
      contentElement.innerHTML = this.formatContent();
      return;
    }

    // 获取新增的文本
    const addedText = newContent.slice(oldContent.length);

    // 如果没有新增文本，直接返回
    if (!addedText) {
      return;
    }

    console.log(`🎯 Adding text: "${addedText}"`);

    // 🎯 真正的打字机效果实现
    // 清除现有的光标
    this.clearStreamingCursor(contentElement);

    // 设置当前内容（不包含新增文本）
    contentElement.innerHTML = oldContent;

    // 创建一个容器来显示新增的文本
    const streamingSpan = document.createElement('span');
    streamingSpan.className = 'streaming-text-container';
    contentElement.appendChild(streamingSpan);

    // 添加光标
    const cursor = document.createElement('span');
    cursor.className = 'streaming-cursor';
    cursor.textContent = '▋';
    cursor.style.animation = 'blink 1s infinite';
    contentElement.appendChild(cursor);

    // 逐字符添加新文本
    let charIndex = 0;
    const typewriterInterval = setInterval(() => {
      if (charIndex < addedText.length) {
        streamingSpan.textContent += addedText[charIndex];
        charIndex++;
        this.scrollToBottom();
      } else {
        // 动画完成，清理并设置最终内容
        clearInterval(typewriterInterval);
        contentElement.innerHTML = newContent;

        // 如果还在流式状态，重新添加光标
        if (this.status === MessageStatus.STREAMING) {
          const finalCursor = document.createElement('span');
          finalCursor.className = 'streaming-cursor';
          finalCursor.textContent = '▋';
          finalCursor.style.animation = 'blink 1s infinite';
          contentElement.appendChild(finalCursor);
        }
      }
    }, 30); // 30ms per character for smooth typing effect
  }

  /**
   * Start typewriter effect for completely new content
   * @param {HTMLElement} contentElement Content element
   * @param {string} text Text to display
   */
  startTypewriterEffect(contentElement, text) {
    console.log(`🎯 Starting typewriter effect for: "${text}"`);

    // 清除现有内容
    contentElement.innerHTML = '';

    // 创建文本容器
    const textContainer = document.createElement('span');
    textContainer.className = 'streaming-text-container';
    contentElement.appendChild(textContainer);

    // 添加光标
    const cursor = document.createElement('span');
    cursor.className = 'streaming-cursor';
    cursor.textContent = '▋';
    cursor.style.animation = 'blink 1s infinite';
    contentElement.appendChild(cursor);

    // 逐字符显示文本
    let charIndex = 0;
    const typewriterInterval = setInterval(() => {
      if (charIndex < text.length) {
        textContainer.textContent += text[charIndex];
        charIndex++;
        this.scrollToBottom();
      } else {
        // 完成后保持光标（如果还在流式状态）
        clearInterval(typewriterInterval);
        if (this.status !== MessageStatus.STREAMING) {
          this.clearStreamingCursor(contentElement);
          contentElement.innerHTML = text;
        }
      }
    }, 30);
  }

  /**
   * Clear streaming cursor from content element
   * @param {HTMLElement} contentElement Content element
   */
  clearStreamingCursor(contentElement) {
    const cursor = contentElement.querySelector('.streaming-cursor');
    if (cursor) {
      cursor.remove();
    }
  }

  /**
   * Scroll to bottom of chat container
   */
  scrollToBottom() {
    const messageElement = document.getElementById(`message-${this.id}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }

  /**
   * Add audio data to message
   * @param {string} audioData Base64 encoded audio data
   */
  addAudioData(audioData) {
    this.audioData = audioData;

    // Update DOM if message is rendered
    const messageElement = document.getElementById(`message-${this.id}`);
    if (messageElement) {
      const bodyElement = messageElement.querySelector(".message-body");
      if (bodyElement && !bodyElement.querySelector(".audio-controls")) {
        bodyElement.insertAdjacentHTML("beforeend", this.renderAudioControls());
        this.attachEventListeners(messageElement);
      }
    }
  }

  /**
   * Toggle audio playback
   */
  toggleAudioPlayback() {
    if (this.audioData) {
      // This will be implemented with AudioManager integration
      console.log(`Toggle audio playback for message ${this.id}`);

      // Update playing status
      this.isAudioPlaying = !this.isAudioPlaying;
      this.status = this.isAudioPlaying
        ? MessageStatus.PLAYING
        : MessageStatus.COMPLETED;

      // Update play button
      const messageElement = document.getElementById(`message-${this.id}`);
      if (messageElement) {
        const playBtn = messageElement.querySelector(".audio-play-btn");
        if (playBtn) {
          playBtn.textContent = this.isAudioPlaying ? "⏸️" : "▶️";
        }

        messageElement.className = this.getMessageClasses();
      }
    }
  }

  /**
   * Mark message as completed
   */
  markCompleted() {
    this.updateContent(this.content, MessageStatus.COMPLETED);
  }

  /**
   * Mark message as error
   * @param {string} errorMessage Error message
   */
  markError(errorMessage = "发送失败") {
    this.updateContent(errorMessage, MessageStatus.ERROR);
  }
}

/**
 * Chat Message Manager
 * Manages the collection of chat messages
 */
class ChatMessageManager {
  constructor(containerElement) {
    this.container = containerElement;
    this.messages = new Map();
    this.messageIdCounter = 0;
    this.currentMode = "standard"; // 当前模式：standard 或 interviewer
    this.allMessages = new Map(); // 存储所有消息，包括被过滤的
    this.seenMessages = new Set(); // 用于去重的消息哈希集合
    this.messageTimeWindow = 5000; // 5秒内的重复消息会被过滤
  }

  /**
   * Generate a unique key for message deduplication
   * @param {string} type Message type
   * @param {string} content Message content
   * @param {string} messageKey Optional message key from server
   * @returns {string} Unique message key
   */
  generateMessageKey(type, content, messageKey = null) {
    if (messageKey) {
      return messageKey;
    }

    // 生成基于内容和时间的哈希
    const contentHash = this.simpleHash(content);
    const timeWindow = Math.floor(Date.now() / this.messageTimeWindow);
    return `${type}_${contentHash}_${timeWindow}`;
  }

  /**
   * Simple hash function for content
   * @param {string} str String to hash
   * @returns {number} Hash value
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Check if message is duplicate
   * @param {string} messageKey Message key for deduplication
   * @returns {boolean} True if message is duplicate
   */
  isDuplicate(messageKey) {
    if (this.seenMessages.has(messageKey)) {
      return true;
    }

    // 添加到已见消息集合
    this.seenMessages.add(messageKey);

    // 清理过期的消息键（保持集合大小合理）
    if (this.seenMessages.size > 1000) {
      // 清空一半最旧的记录
      const keysArray = Array.from(this.seenMessages);
      this.seenMessages = new Set(keysArray.slice(500));
    }

    return false;
  }

  /**
   * Set the current display mode
   * @param {string} mode - Display mode (standard, interviewer)
   */
  setMode(mode) {
    this.currentMode = mode;
    console.log(`📋 Chat display mode changed to: ${mode}`);
    this.refreshDisplay();
  }

  /**
   * Get current mode
   * @returns {string} Current mode
   */
  getMode() {
    return this.currentMode;
  }

  /**
   * Check if a message should be displayed based on current mode
   * @param {string} messageType - Message type (user, assistant, system)
   * @returns {boolean} Whether message should be displayed
   */
  shouldDisplayMessage(messageType) {
    // Always show all messages in both modes - this was filtering too aggressively
    return true;

    // Old logic that was hiding AI messages in interviewer mode:
    // switch (this.currentMode) {
    //   case "interviewer":
    //     // 在面试官模式下，只显示用户消息和系统消息
    //     return (
    //       messageType === MessageType.USER || messageType === MessageType.SYSTEM
    //     );
    //   case "standard":
    //   default:
    //     // 在标准模式下，显示所有消息
    //     return true;
    // }
  }

  /**
   * Refresh the display based on current mode
   */
  refreshDisplay() {
    // 清空当前显示
    this.container.innerHTML = "";
    this.messages.clear();

    // 重新添加应该显示的消息
    for (const [messageId, message] of this.allMessages) {
      if (this.shouldDisplayMessage(message.type)) {
        this.messages.set(messageId, message);
        const messageElement = message.render();
        this.container.appendChild(messageElement);
      }
    }

    this.scrollToBottom();
  }

  /**
   * Generate unique message ID
   * @returns {string} Unique message ID
   */
  generateMessageId() {
    return `msg_${++this.messageIdCounter}_${Date.now()}`;
  }

  /**
   * Add new message to chat with deduplication and proper ordering
   * @param {string} type Message type
   * @param {string} content Message content
   * @param {string} status Message status
   * @param {string} messageKey Optional message key for deduplication
   * @returns {ChatMessage|null} Created message or null if duplicate
   */
  addMessage(
    type,
    content = "",
    status = MessageStatus.COMPLETED,
    messageKey = null
  ) {
    // 🎯 重复消息检测
    const dedupKey = this.generateMessageKey(type, content, messageKey);
    if (this.isDuplicate(dedupKey)) {
      console.log(
        `🔄 重复消息被过滤: ${type} - ${content.substring(0, 50)}...`
      );
      return null;
    }

    const messageId = this.generateMessageId();
    const message = new ChatMessage(messageId, type, content, status);

    // 总是将消息添加到完整消息列表
    this.allMessages.set(messageId, message);

    // 根据当前模式决定是否显示
    if (this.shouldDisplayMessage(type)) {
      this.messages.set(messageId, message);

      // 🎯 修复：按时间戳排序插入消息
      const messageElement = message.render();

      // 找到正确的插入位置（按时间戳排序）
      const existingMessages = Array.from(this.container.children);
      let insertPosition = existingMessages.length;

      for (let i = 0; i < existingMessages.length; i++) {
        const existingMessageId =
          existingMessages[i].getAttribute("data-message-id");
        const existingMessage = this.messages.get(existingMessageId);

        if (
          existingMessage &&
          existingMessage.timestampMs > message.timestampMs
        ) {
          insertPosition = i;
          break;
        }
      }

      if (insertPosition >= existingMessages.length) {
        this.container.appendChild(messageElement);
      } else {
        this.container.insertBefore(
          messageElement,
          existingMessages[insertPosition]
        );
      }

      // Scroll to bottom
      this.scrollToBottom();
    } else {
      console.log(
        `🔽 Message filtered out due to mode ${
          this.currentMode
        }: ${type} - ${content.substring(0, 50)}...`
      );
    }

    return message;
  }

  /**
   * Update existing message with optional streaming effect
   * @param {string} messageId Message ID
   * @param {string} content New content
   * @param {string} status New status
   * @param {boolean} useStreamingEffect Whether to use streaming effect
   */
  updateMessage(messageId, content, status = null, useStreamingEffect = false) {
    // 更新完整消息列表中的消息
    const allMessage = this.allMessages.get(messageId);
    if (allMessage) {
      allMessage.updateContent(content, status, useStreamingEffect);
    }

    // 更新显示消息列表中的消息
    const message = this.messages.get(messageId);
    if (message) {
      message.updateContent(content, status, useStreamingEffect);
    }
  }

  /**
   * Get message by ID
   * @param {string} messageId Message ID
   * @returns {ChatMessage|null} Message instance
   */
  getMessage(messageId) {
    return this.allMessages.get(messageId) || null;
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    this.messages.clear();
    this.allMessages.clear();
    this.container.innerHTML = "";
    this.messageIdCounter = 0;
  }

  /**
   * Scroll chat to bottom
   */
  scrollToBottom() {
    const oldScrollTop = this.container.scrollTop;
    this.container.scrollTop = this.container.scrollHeight;

    // 🎯 调试：记录滚动操作
    console.log(`🎯 DEBUG: Scrolled chat to bottom. Old position: ${oldScrollTop}, New position: ${this.container.scrollTop}, Container height: ${this.container.scrollHeight}`);
  }

  /**
   * Add typing indicator for AI
   * @returns {string} Message ID of typing indicator
   */
  addTypingIndicator() {
    return this.addMessage(MessageType.ASSISTANT, "", MessageStatus.TYPING).id;
  }

  /**
   * Remove typing indicator
   * @param {string} messageId Typing indicator message ID
   */
  removeTypingIndicator(messageId) {
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.remove();
    }
    this.messages.delete(messageId);
    this.allMessages.delete(messageId);
  }

  /**
   * Convert typing indicator to actual message
   * @param {string} typingMessageId Typing indicator message ID
   * @param {string} content Actual message content
   * @returns {string} New message ID
   */
  convertTypingToMessage(typingMessageId, content) {
    // Remove typing indicator
    this.removeTypingIndicator(typingMessageId);

    // Add actual message
    return this.addMessage(
      MessageType.ASSISTANT,
      content,
      MessageStatus.COMPLETED
    ).id;
  }
}

// Export for use in other modules
window.ChatMessage = ChatMessage;
window.ChatMessageManager = ChatMessageManager;
window.MessageStatus = MessageStatus;
window.MessageType = MessageType;
