"""
自定义异常类定义for Audio Agent系统
"""


class AudioAgentError(Exception):
    """Base exception for audio agent system"""
    pass


class AgentInitializationError(AudioAgentError):
    """Raised when agent fails to initialize"""
    pass


class ConfigurationError(AudioAgentError):
    """Raised when configuration is invalid"""
    pass


class APIClientError(AudioAgentError):
    """Raised when AI API client encounters error"""
    pass


class WebSocketConnectionError(AudioAgentError):
    """Raised when WebSocket connection fails"""
    pass


class MessageBusError(AudioAgentError):
    """Raised when message bus operations fail"""
    pass


class AgentStateError(AudioAgentError):
    """Raised when agent state is invalid for requested operation"""
    pass 