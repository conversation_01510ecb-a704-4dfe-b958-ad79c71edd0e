FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user with home directory
RUN groupadd -r appuser && useradd -r -g appuser -m appuser

# Copy requirements and install Python dependencies
COPY requirements.txt .

# Install dependencies with increased timeout
RUN pip install --no-cache-dir --timeout 300 -r requirements.txt

# Copy application code
COPY . .

# Create directories and set permissions
RUN mkdir -p /app/logs /home/<USER>/.cache && \
    chown -R appuser:appuser /app /home/<USER>
    chmod -R 755 /home/<USER>

# Copy supervisor configuration
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Install supervisor
USER root
RUN pip install supervisor

# Make start script executable
RUN chmod +x /app/docker/start.sh

# Switch to non-root user
USER appuser

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["/app/docker/start.sh"] 