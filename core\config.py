"""
Configuration management for the Audio Agent system.
"""

import os
from typing import Optional, Dict, Any
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """
    System configuration using Pydantic Settings.
    Automatically loads from environment variables.
    """
    
    # 阿里千问 配置 (用于Realtime API - Listener和Speaker)
    qwen_api_key: str = Field(
        description="阿里千问 API Key"
    )
    qwen_realtime_model: str = Field(
        default="qwen-omni-turbo-realtime",
        description="阿里千问-Realtime API model name"
    )
    qwen_base_url: str = Field(
        default="https://dashscope.aliyuncs.com/api-ws/v1/",
        description="阿里千问 API base URL"
    )
    qwen_ws_endpoint: str = Field(
        default="wss://dashscope.aliyuncs.com/api-ws/v1/realtime",
        description="阿里千问 WebSocket endpoint"
    )
    
    # 豆包 配置 (用于Tactical Thinker、Strategic Thinker和Decider)
    doubao_api_key: str = Field(
        description="字节跳动豆包 API Key"
    )
    doubao_base_url: str = Field(
        default="https://ark.cn-beijing.volces.com/api/v3",
        description="豆包 API base URL"
    )
    
    # 音频配置
    audio_sample_rate: int = Field(default=24000, description="Audio sample rate")
    audio_channels: int = Field(default=1, description="Audio channels")
    audio_format: str = Field(default="pcm16", description="Audio format")
    audio_chunk_size: int = Field(default=1024, description="Audio chunk size")
    
    # 系统配置
    log_level: str = Field(default="INFO", description="Logging level")
    max_conversations: int = Field(default=10, description="Maximum concurrent conversations")
    interruption_threshold: float = Field(default=0.5, description="General interruption threshold")
    
    # WebSocket配置
    ws_host: str = Field(default="0.0.0.0", description="WebSocket host")
    ws_port: int = Field(default=8000, description="WebSocket port")
    
    # AI Agent配置 - 豆包模型
    tactical_thinker_model: str = Field(default="doubao-1-5-lite-32k-250115", description="Tactical thinker model")
    strategic_thinker_model: str = Field(default="doubao-1-5-lite-32k-250115", description="Strategic thinker model")
    decider_model: str = Field(default="doubao-1-5-lite-32k-250115", description="Decider model")
    
    # 🎯 降低打断阈值以提高触发率
    cooperative_interruption_threshold: float = 0.2  # 从0.4降低到0.2，按照用户规格
    disruptive_interruption_threshold: float = 0.15   # 从0.3降低到0.15，按照用户规格
    silence_duration_ms: int = Field(default=100, description="Silence duration in milliseconds - lowered for real-time transcription")
    response_delay_ms: int = Field(default=100, description="Response delay in milliseconds")

    # 🎯 新增：Tactical Thinker 缓冲区配置
    tactical_buffer_min_length: int = Field(default=15, description="Tactical thinker minimum buffer length in Chinese characters")
    tactical_buffer_max_length: int = Field(default=150, description="Tactical thinker maximum buffer length in Chinese characters")

    # 🎯 新增：Strategic Thinker 缓冲区配置  
    strategic_buffer_min_length: int = Field(default=15, description="Strategic thinker minimum buffer length in Chinese characters")
    strategic_buffer_max_length: int = Field(default=500, description="Strategic thinker maximum buffer length in Chinese characters")
    
    # Decider agent settings - 战略分析超时设置
    decider_strategic_timeout: int = 3  # 从5秒降低到3秒，加快决策速度

    # 🎯 VAD (语音活动检测) 配置 - 简化为两个模式
    # STANDARD模式: 标准对话模式 (包括interview场景)
    vad_standard_threshold: float = Field(default=0.5, description="VAD threshold for standard mode")
    vad_standard_prefix_padding_ms: int = Field(default=200, description="VAD prefix padding for standard mode")
    vad_standard_silence_duration_ms: int = Field(default=900, description="VAD silence duration for standard mode")

    # BARGE_IN模式: 打断模式 (AI说话时用户插话)
    vad_barge_in_threshold: float = Field(default=0.3, description="VAD threshold for barge-in mode")
    vad_barge_in_prefix_padding_ms: int = Field(default=200, description="VAD prefix padding for barge-in mode")
    vad_barge_in_silence_duration_ms: int = Field(default=600, description="VAD silence duration for barge-in mode")

    # 🎯 面试官模式配置（修复字段定义）
    interviewer_mode: bool = False  # 添加缺失的字段
    interviewer_cooperative_threshold: float = 0.1    # 从0.4降低到0.3
    interviewer_disruptive_threshold: float = 0.2     # 从0.3降低到0.2
    interviewer_max_speaking_time: int = 15            # 用户最大发言时间（秒）
    interviewer_force_interrupt_after: int = 30       # 强制打断时间（秒）
    interviewer_topic_drift_threshold: float = 0.6    # 话题偏移阈值
    interviewer_verbosity_threshold: float = 0.7      # 🎯 修复：改为float类型匹配env值
    
    # 分析器超时配置
    strategic_analysis_timeout: int = Field(default=100000, description="Strategic analysis timeout - increased")
    tactical_analysis_timeout: int = Field(default=100000, description="Tactical analysis timeout - increased")
    
    # 连接管理配置
    auto_reconnect: bool = Field(default=True, description="Enable automatic reconnection")

    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()

# 面试官模式指令常量
INTERVIEWER_INSTRUCTIONS = """你是一个专业的AI技术面试官。你的职责是：
1. 主动引导面试流程，控制节奏
2. 在候选人偏离主题时及时打断
3. 深入挖掘技术细节，追问实现原理
4. 评估候选人的技术深度和思维逻辑
5. 保持专业但友好的面试氛围

打断策略：
- 当回答偏离技术主题时立即打断
- 当回答过于冗长或缺乏重点时适时打断  
- 当需要深入某个技术点时主动打断追问
- 控制每个问题的回答时长，保持面试效率"""

# 🎯 新增：定义标准模式下的AI身份
ADVENTUREX_PERSONA = """You are Audio Manus, a friendly and professional voice assistant from the AdventureX events team. Your primary role is to have a natural, helpful, and engaging conversation. You should be proactive, but always polite. Do not start the conversation. Wait for the user to speak first. Keep your responses concise and to the point."""

# 将其整合到STANDARD_INSTRUCTIONS
STANDARD_INSTRUCTIONS = ADVENTUREX_PERSONA


def get_realtime_config() -> Dict[str, Any]:
    """Get 阿里千问 realtime API configuration dictionary."""
    return {
        "api_key": settings.qwen_api_key,
        "model": settings.qwen_realtime_model,
        "base_url": settings.qwen_base_url,
        "ws_endpoint": settings.qwen_ws_endpoint
    }


def get_qwen_voice_mapping() -> Dict[str, str]:
    """Get voice name mapping for qwen API."""
    return {
        "shimmer": "Chelsie",
        "alloy": "Serena", 
        "echo": "Serena",
        "fable": "Ethan",
        "onyx": "Ethan",
        "nova": "Ethan",
        "Mike": "Ethan",
        "Alice": "Serena", 
        "Jenny": "Serena",
        "Ethan": "Ethan",
        "Cherry": "Cherry"
    }


def get_doubao_config() -> Dict[str, Any]:
    """Get 字节跳动豆包 API configuration dictionary."""
    return {
        "api_key": settings.doubao_api_key,
        "base_url": settings.doubao_base_url
    }


def get_audio_config() -> Dict[str, Any]:
    """Get audio configuration dictionary."""
    return {
        "sample_rate": settings.audio_sample_rate,
        "channels": settings.audio_channels,
        "format": settings.audio_format,
        "chunk_size": settings.audio_chunk_size
    } 