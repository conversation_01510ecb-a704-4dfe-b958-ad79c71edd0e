好的，我们来将所有核心部件串联起来，总结整个系统从接收用户声音到 AI 做出响应（包括主动打断）的完整链路步骤。

这个系统的设计精髓在于**两条并行处理的流水线**：一条是用于**常规问答**的"深度思考"慢速通道，另一条是用于**实时打断**的"快速反应"高速通道。

### 整体链路图示

```mermaid
sequenceDiagram
    participant C as Client (JS)
    participant B as Backend (app.py)
    participant RM as RealtimeManager
    participant MB as MessageBus
    participant TT as TacticalThinker
    participant ST as StrategicThinker
    participant D as Decider
    participant O as Orchestrator
    participant S as Speaker

    Note over C,S: 用户正在说话...
    C->>B: 持续发送音频流
    B->>RM: send_audio_buffer_append()
    RM->>MB: publish(TRANSCRIPT_DELTA)

    Note over TT,D: --- 快速反应通道 (合作型附和) ---
    MB-->>TT: on(TRANSCRIPT_DELTA)
    TT->>TT: 上下文感知语义分析
    alt 发现合作信号
        TT->>MB: publish(UI_UPDATE)
        MB-->>B: on(UI_UPDATE)
        B->>C: WebSocket send(cooperative_interjection)
        Note right of C: 前端显示emoji文字附和<br/>如：【确实是👍】
    end

    Note over C,S: 用户停止说话...
    RM->>MB: publish(TRANSCRIPT_COMPLETED)

    Note over ST,D: --- 深度思考通道 (常规回复/干扰型打断) ---
    MB-->>ST: on(TRANSCRIPT_COMPLETED)
    ST->>ST: (后台)深度上下文分析
    alt 发现干扰信号 (例如跑题/冗长)
        ST->>MB: publish(DISRUPTIVE_ANALYSIS)
        MB-->>D: on(DISRUPTIVE_ANALYSIS)
        D->>D: 评估cooldown状态
        D->>MB: publish(INTERRUPTION_DECISION)
        MB-->>O: on(INTERRUPTION_DECISION)
        O->>S: 通过MB发送语音打断指令
        S->>RM: cancel_response() / create_response()
        RM-->>C: AI打断语音
    else 常规回复
        ST->>MB: publish(STRATEGIC_ANALYSIS)
        MB-->>O: on(STRATEGIC_ANALYSIS)
        O->>S: 通过MB发送常规回复指令
        S->>RM: create_response()
        RM-->>C: AI回复语音
    end
```

---

### 详细链路步骤分解

#### 场景 A: 标准问答流程 (用户说完，AI 回答)

1.  **感知 (Perception)**

    - **步骤 1: 音频采集与传输**: 用户在前端讲话，`audio.js` 采集 PCM 音频数据，通过 WebSocket 持续发送给后端 `app.py`。
    - **步骤 2: 音频转发**: `app.py` 接收到音频块，调用 `Listener` 的 `process_audio_chunk` 方法。
    - **步骤 3: API 交互**: `Listener` 实际上是一个代理，它将音频数据交给唯一的 `RealtimeManager` 实例，由 `RealtimeManager` 发送给阿里千问 API。
    - **步骤 4: 语音转写**: 千问 API 进行实时语音识别。当检测到用户停顿（VAD），它会发回一个 `conversation.item.input_audio_transcription.completed` 事件，包含最终的完整转写结果。
    - **步骤 5: 情报发布**: `RealtimeManager` 接收到这个事件，将其标准化后，通过 `MessageBus` 发布到 `TopicNames.TRANSCRIPT_COMPLETED` 主题。

2.  **决策 (Decision)**

    - **步骤 6: 战略思考**: `StrategicThinker` 订阅了 `TRANSCRIPT_COMPLETED` 主题，因此被激活。它将完整的用户语句存入自己的**对话记忆 (`conversation_context`)**，然后调用豆包模型生成一个深度的、上下文感知的回复。
    - **步骤 7: 发布回复内容**: `StrategicThinker` 完成思考后，将生成的回复内容发布到 `TopicNames.STRATEGIC_ANALYSIS` 主题（这里也可以理解为常规的 `RESPONSE_GENERATED`）。

3.  **反应 (Reaction)**

    - **步骤 8: 接收回复**: `Orchestrator` 监听 `STRATEGIC_ANALYSIS` 主题，接收到 AI 准备好的回复文本。
    - **步骤 9: 下达指令**: `Orchestrator` 作为总导演，更新会话状态为 `AI_SPEAKING`，然后通过 `MessageBus` 向 `Speaker` 发布一个 `start_tts` 指令，内容为 AI 的回复文本。
    - **步骤 10: 合成语音**: `Speaker` 接收到指令，调用 `RealtimeManager` 的接口，向千问 API 发送 `response.create` 事件，请求合成语音。
    - **步骤 11: 音频回传**: 千问 API 开始发回流式的 `response.audio.delta` 音频数据。`RealtimeManager` 接收到这些数据。
    - **步骤 12: 直接音频通道**: `RealtimeManager` 通过其维护的**直接音频通道**，将音频数据块直接、低延迟地发送回前端 `app.js`。
    - **步骤 13: 播放音频**: 前端的 `DirectAudioPlayer` 接收并播放音频流，用户听到 AI 的回复。

---

#### 场景 B: AI 主动打断流程 (用户正在说，AI 介入)

### B1: 合作型附和 (文字显示)

1.  **感知 (Perception)**

    - **步骤 1-3 (同上)**: 用户的音频流持续发送到千问 API。
    - **步骤 4 (不同点)**: 千问 API **在用户说话的同时**，持续发回 `conversation.item.input_audio_transcription.delta` 事件，每个事件包含一小段实时识别的词语。
    - **步骤 5 (不同点)**: `RealtimeManager` 接收到这些 `delta` 事件，并立即发布到 `MessageBus` 的 `TopicNames.TRANSCRIPT_DELTA` 主题。

2.  **决策 (Decision) - 合作型快速通道**

    - **步骤 6: 上下文感知分析**: `TacticalThinker` 订阅了 `TRANSCRIPT_DELTA`，因此**每个语音片段都会触发它**。它使用豆包模型进行**完整的语义理解和上下文分析**，结合 20 轮对话历史判断用户的情感状态和支持需求。
    - **步骤 7: 发布文字附和**: 如果 `TacticalThinker` 发现了合作信号（例如，用户表现出困惑或表达观点），它会立刻向 `TopicNames.UI_UPDATE` 主题发布一个**文字附和建议**。
    - **步骤 8: 直接显示**: `app.py` 的 `ui_update_callback` 接收到附和建议，直接通过 WebSocket 发送 `cooperative_interjection` 消息给前端。

3.  **反应 (Reaction) - 文字显示**

    - **步骤 9: 前端展示**: 前端接收到 `cooperative_interjection` 消息，在 UI 上显示文字附和（如"【确实是 👍】"、"【没事的】"），**无语音合成**，用户看到 AI 的实时文字反馈。

### B2: 干扰型打断 (语音介入)

1.  **感知 (Perception)** - 同 B1

2.  **决策 (Decision) - 干扰型深度通道**

    - **步骤 6: 深度流程分析**: `StrategicThinker` 同时监听 `TRANSCRIPT_DELTA` 和 `TRANSCRIPT_COMPLETED`，进行**实时流式分析**。它维护 20 轮对话历史，检测重复性、冗长性、话题偏移等模式。
    - **步骤 7: 发布干扰建议**: 如果 `StrategicThinker` 检测到需要干预的模式，它会向 `TopicNames.DISRUPTIVE_ANALYSIS` 主题发布一个**语音打断建议**，包含完整的打断话术。
    - **步骤 8: 决策仲裁**: `Decider` 接收到干扰型打断建议。它会检查当前是否在 cooldown 期间，如果没有，就立即做出**最终的打断决策**。
    - **步骤 9: 发布最终决策**: `Decider` 将包含打断类型、原因和建议话术的最终决策发布到 `TopicNames.INTERRUPTION_DECISION` 主题。

3.  **反应 (Reaction) - 语音打断**

    - **步骤 10: 导演执行**: `Orchestrator` 接收到最终的打断决策，立即调用 `_execute_interruption` 方法。
    - **步骤 11: 执行中断**: `Orchestrator` 执行一系列动作：
      - 向 `Speaker` 发送中断指令，停止任何可能正在播放的 AI 语音。
      - 向 `Speaker` 发送 `start_tts` 指令，内容为 `Decider` 建议的打断话术（例如，"请简洁一些，重点是什么？"）。
      - （可选）暂时让 `RealtimeManager` 忽略用户语音，防止 AI 的打断被再次打断。
    - **步骤 12-13 (同场景 A 的 10-13)**: `Speaker` 请求合成打断话术的语音，`RealtimeManager` 通过直接通道将音频发回前端播放，用户听到 AI 的主动介入。

---

### 🎯 新架构的关键优势

1.  **双模式并行**: 合作型文字附和和干扰型语音打断完全并行处理，互不干扰
2.  **上下文感知**: 两个 Thinker 都维护完整对话历史，提供高质量的语义理解
3.  **事件驱动**: 移除所有超时等待，实现真正的实时响应
4.  **用户体验**: 文字附和更自然，语音打断更精准

这个双通道、并发处理的链路是整个系统的核心，它使得系统既能进行有深度的对话，又能做出快速、自然的实时反应，是实现您宏大愿景的坚实基础。
