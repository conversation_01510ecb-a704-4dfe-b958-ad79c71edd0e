"""
Voice Activity Detection (VAD) Manager - 简化版本

统一管理VAD配置，只提供两个模式：
1. STANDARD - 标准模式，用于正常对话（包括interview）
2. BARGE_IN - 打断模式，用于AI说话时用户插话需要快速停止音频
"""

from dataclasses import dataclass, asdict
from enum import Enum
from typing import Dict, Any, Optional
import asyncio
from loguru import logger
from core.config import settings


class VADMode(str, Enum):
    """VAD检测模式"""
    DISABLED = "disabled"
    SERVER_VAD = "server_vad"
    CLIENT_VAD = "client_vad"


class VADPreset(str, Enum):
    """VAD预设配置 - 简化为两个模式"""
    STANDARD = "standard"      # 标准模式：正常对话，包括interview场景
    BARGE_IN = "barge_in"      # 打断模式：AI说话时用户插话，需要快速检测和停止


@dataclass
class VADConfig:
    """VAD配置数据类"""
    mode: VADMode = VADMode.SERVER_VAD
    threshold: float = 0.85
    prefix_padding_ms: int = 500
    silence_duration_ms: int = 1300
    create_response: bool = True
    interrupt_response: bool = True
    
    def validate(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 阈值范围检查
            if not (-1.0 <= self.threshold <= 1.0):
                logger.error(f"VAD threshold {self.threshold} out of range [-1.0, 1.0]")
                return False
            
            # 时间参数检查
            if self.prefix_padding_ms < 0:
                logger.error(f"VAD prefix_padding_ms {self.prefix_padding_ms} cannot be negative")
                return False
            
            if self.silence_duration_ms < 100:
                logger.error(f"VAD silence_duration_ms {self.silence_duration_ms} too small (min: 100ms)")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"VAD config validation error: {e}")
            return False
    
    def to_qwen_config(self) -> Dict[str, Any]:
        """转换为千问API格式的配置"""
        if self.mode == VADMode.DISABLED:
            return {"type": "none"}
        
        return {
            "type": self.mode.value,
            "threshold": max(-1.0, min(1.0, self.threshold)),
            "prefix_padding_ms": max(100, self.prefix_padding_ms),
            "silence_duration_ms": max(100, self.silence_duration_ms),
            "create_response": self.create_response,
            "interrupt_response": self.interrupt_response
        }
    
    def copy(self) -> 'VADConfig':
        """创建配置副本"""
        return VADConfig(**asdict(self))


class VADManager:
    """VAD管理器 - 简化版本，只管理两个模式"""
    
    def __init__(self, realtime_manager=None):
        self.realtime_manager = realtime_manager
        self.current_config = VADConfig()
        self.current_preset = VADPreset.STANDARD
        self.presets = self._create_presets()
        
    def _create_presets(self) -> Dict[VADPreset, VADConfig]:
        """创建预设配置 - 只有两个模式，使用配置文件中的值"""
        return {
            VADPreset.STANDARD: VADConfig(
                mode=VADMode.SERVER_VAD,
                threshold=settings.vad_standard_threshold,
                prefix_padding_ms=settings.vad_standard_prefix_padding_ms,
                silence_duration_ms=settings.vad_standard_silence_duration_ms,
                create_response=True,
                interrupt_response=True
            ),

            VADPreset.BARGE_IN: VADConfig(
                mode=VADMode.SERVER_VAD,
                threshold=settings.vad_barge_in_threshold,
                prefix_padding_ms=settings.vad_barge_in_prefix_padding_ms,
                silence_duration_ms=settings.vad_barge_in_silence_duration_ms,
                create_response=True,
                interrupt_response=True     # 关键：必须允许打断
            )
        }
    
    async def apply_preset(self, preset: VADPreset) -> bool:
        """应用预设配置"""
        try:
            if preset not in self.presets:
                logger.error(f"Unknown VAD preset: {preset}")
                return False
            
            preset_config = self.presets[preset].copy()
            success = await self.configure(preset_config)
            
            if success:
                self.current_preset = preset
                logger.info(f"Applied VAD preset: {preset}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error applying VAD preset {preset}: {e}")
            return False
    
    async def configure(self, config: VADConfig) -> bool:
        """配置VAD参数"""
        try:
            # 验证配置
            if not config.validate():
                logger.error("VAD configuration validation failed")
                return False
            
            # 更新配置
            self.current_config = config.copy()
            
            # 应用到realtime_manager（如果可用且已连接）
            if self.realtime_manager:
                # 🎯 修复：检查realtime_manager状态，避免在session未激活时配置
                if not self.realtime_manager.is_connected or not self.realtime_manager.is_session_active:
                    logger.warning("Realtime manager not ready, VAD config stored but not applied yet")
                    return True  # 配置已保存，稍后会应用
                
                qwen_config = config.to_qwen_config()
                success = await self.realtime_manager.update_session_config({
                    "turn_detection": qwen_config
                })
                
                if not success:
                    logger.error("Failed to apply VAD config to realtime manager")
                    return False
                
                logger.info(f"VAD configured and applied to realtime manager: {config}")
            else:
                logger.warning("No realtime manager available, VAD config stored only")
            
            return True
            
        except Exception as e:
            logger.error(f"Error configuring VAD: {e}")
            return False
    
    async def switch_to_standard(self) -> bool:
        """切换到标准模式"""
        return await self.apply_preset(VADPreset.STANDARD)
    
    async def switch_to_barge_in(self) -> bool:
        """切换到打断模式"""
        return await self.apply_preset(VADPreset.BARGE_IN)
    
    def get_current_preset(self) -> VADPreset:
        """获取当前预设"""
        return self.current_preset
    
    def get_config(self) -> VADConfig:
        """获取当前配置"""
        return self.current_config.copy()
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前VAD状态"""
        return {
            "current_preset": self.current_preset.value,
            "config": asdict(self.current_config),
            "available_presets": [preset.value for preset in VADPreset],
            "is_enabled": self.current_config.mode != VADMode.DISABLED
        }
    
    def set_realtime_manager(self, realtime_manager):
        """设置realtime_manager引用"""
        self.realtime_manager = realtime_manager
        logger.debug("VADManager: realtime_manager reference updated")
    
    async def apply_stored_config_if_ready(self) -> bool:
        """如果realtime_manager已准备好，应用存储的VAD配置"""
        if (self.realtime_manager and 
            self.realtime_manager.is_connected and 
            self.realtime_manager.is_session_active and
            self.current_config):
            
            try:
                qwen_config = self.current_config.to_qwen_config()
                success = await self.realtime_manager.update_session_config({
                    "turn_detection": qwen_config
                })
                
                if success:
                    logger.info(f"Stored VAD config applied successfully: {self.current_config}")
                    return True
                else:
                    logger.error("Failed to apply stored VAD config")
                    return False
                    
            except Exception as e:
                logger.error(f"Error applying stored VAD config: {e}")
                return False
        
        return False
    
    async def temporarily_disable_auto_response(self) -> bool:
        """
        临时禁用VAD的自动响应创建功能
        用于防止VAD与AI主动打断产生冲突
        
        Returns:
            bool: 是否成功禁用
        """
        try:
            if not self.realtime_manager or not self.realtime_manager.is_connected:
                logger.warning("Cannot disable auto response: realtime manager not ready")
                return False
            
            # 获取当前配置的副本
            temp_config = self.current_config.copy()
            temp_config.create_response = False  # 临时禁用自动响应
            
            qwen_config = temp_config.to_qwen_config()
            success = await self.realtime_manager.update_session_config({
                "turn_detection": qwen_config
            })
            
            if success:
                logger.info("🚫 VAD auto-response temporarily disabled to prevent conflicts")
            else:
                logger.error("Failed to disable VAD auto-response")
                
            return success
            
        except Exception as e:
            logger.error(f"Error disabling VAD auto-response: {e}")
            return False
    
    async def restore_auto_response(self) -> bool:
        """
        恢复VAD的自动响应创建功能
        
        Returns:
            bool: 是否成功恢复
        """
        try:
            if not self.realtime_manager or not self.realtime_manager.is_connected:
                logger.warning("Cannot restore auto response: realtime manager not ready")
                return False
            
            # 使用原始配置恢复
            qwen_config = self.current_config.to_qwen_config()
            success = await self.realtime_manager.update_session_config({
                "turn_detection": qwen_config
            })
            
            if success:
                logger.info("✅ VAD auto-response restored to normal operation")
            else:
                logger.error("Failed to restore VAD auto-response")
                
            return success
            
        except Exception as e:
            logger.error(f"Error restoring VAD auto-response: {e}")
            return False


# 全局VAD管理器实例
_global_vad_manager: Optional[VADManager] = None


def get_vad_manager() -> Optional[VADManager]:
    """获取全局VAD管理器实例"""
    return _global_vad_manager


def set_vad_manager(vad_manager: VADManager):
    """设置全局VAD管理器实例"""
    global _global_vad_manager
    _global_vad_manager = vad_manager
    logger.debug("Global VAD manager instance set")


def create_vad_manager(realtime_manager=None) -> VADManager:
    """创建并设置全局VAD管理器实例"""
    vad_manager = VADManager(realtime_manager)
    set_vad_manager(vad_manager)
    logger.info("VAD manager created and set as global instance")
    return vad_manager
