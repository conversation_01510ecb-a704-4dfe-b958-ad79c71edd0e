#!/usr/bin/env python3
"""
启动脚本 - Proactive Real-time AI Conversation Assistant
主动式实时AI对话助手启动脚本

使用方法:
python start.py

环境要求:
- Python 3.8+
- Redis服务器运行中
- Azure OpenAI API密钥配置正确
"""

import asyncio
import sys
import subprocess
import platform
from pathlib import Path
from loguru import logger

# 配置日志
logger.add(
    "logs/audio_agent_{time}.log",
    rotation="1 day",
    retention="7 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"Python版本检查通过: {sys.version}")
    return True



def install_dependencies():
    """安装Python依赖"""
    try:
        logger.info("检查并安装Python依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"依赖安装失败: {result.stderr}")
            return False
            
        logger.info("依赖安装完成")
        return True
    except Exception as e:
        logger.error(f"依赖安装异常: {e}")
        return False


def create_log_directory():
    """创建日志目录"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    logger.info("日志目录已创建")



async def start_agent_system():
    """启动智能体系统"""
    try:
        logger.info("=" * 60)
        logger.info("🚀 启动增强版实时AI对话助手系统")
        logger.info("=" * 60)
        
        # 导入应用程序
        from app import app
        import uvicorn
        from core.config import settings
        
        # 启动FastAPI应用
        logger.info(f"启动WebSocket服务器: {settings.ws_host}:{settings.ws_port}")
        logger.info("")
        logger.info("🎯 系统核心功能:")
        logger.info("  - 实时语音转文字 (阿里千问qwen-omni-turbo-realtime API)")
        logger.info("  - AI主动打断能力 (Dispider架构)")
        logger.info("  - 多智能体协调系统")
        logger.info("  - 多语言自适应对话 (自动检测用户语言)")
        logger.info("  - 智能模式选择 (标准助手/面试官模式)")
        logger.info("")
        logger.info("🧠 双重打断分析架构:")
        logger.info("  - Tactical Thinker: 合作型打断 (同意、协助、澄清)")
        logger.info("  - Strategic Thinker: 干扰型打断 (话题控制、时间管理)")
        logger.info("  - Decider: 优先级决策 (Strategic > Tactical)")
        logger.info("")
        logger.info("🌍 多语言支持:")
        logger.info("  - 自动检测用户输入语言")
        logger.info("  - 智能匹配回复语言")
        logger.info("  - 支持中文、英文及其他主要语言")
        logger.info("")
        logger.info("📡 WebSocket接口:")
        logger.info(f"  - 对话接口: ws://{settings.ws_host}:{settings.ws_port}/ws/conversation")
        logger.info(f"  - 健康检查: http://{settings.ws_host}:{settings.ws_port}/health")
        logger.info(f"  - 系统状态: http://{settings.ws_host}:{settings.ws_port}/status")
        logger.info("")
        logger.info("💡 使用提示:")
        logger.info("  - 连接后系统将询问您选择对话模式")
        logger.info("  - 标准模式: 友好的多语言语音助手")
        logger.info("  - 面试官模式: AI技术面试官，支持打断控制")
        logger.info("  - 支持实时音频流和语音交互")
        logger.info("  - AI会根据您的语言自动调整回复语言")
        logger.info("")
        logger.info("🔧 测试客户端:")
        logger.info("  - 基础测试: python python_test_client_async.py")
        logger.info("  - 详细模式: python python_test_client_async.py 50 --verbose")
        logger.info("=" * 60)
        
        # 启动uvicorn服务器
        config = uvicorn.Config(
            app,
            host=settings.ws_host,
            port=settings.ws_port,
            log_level=settings.log_level.lower(),
            reload=False  # 生产环境建议关闭
        )
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭系统...")
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    logger.info("🔍 开始系统检查...")
    
    # 创建必要目录
    create_log_directory()
    
    # 系统环境检查
    checks = [
        ("Python版本", check_python_version),
    ]
    
    for check_name, check_func in checks:
        logger.info(f"检查 {check_name}...")
        if not check_func():
            logger.error(f"❌ {check_name}检查失败，请修复后重试")
            sys.exit(1)
        logger.info(f"✅ {check_name}检查通过")
    
    # 启动异步系统
    try:
        asyncio.run(start_agent_system())
    except KeyboardInterrupt:
        logger.info("👋 系统已安全关闭")
    except Exception as e:
        logger.error(f"❌ 系统运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 