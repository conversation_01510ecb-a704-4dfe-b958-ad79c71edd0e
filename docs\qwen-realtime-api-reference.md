# Qwen-Omni 实时 API 参考文档

## 🎯 概述

Qwen-Omni 实时模型是一个支持音视频实时对话的多模态大语言模型，具备以下核心特性：

- **🎤 实时音频流处理**: 支持连续音频流输入输出
- **🗣️ 内置语音活动检测**: 自动检测语音开始和结束 (VAD)
- **🎵 多种音色支持**: <PERSON><PERSON><PERSON>、<PERSON>、<PERSON>、Cherry 四种音色
- **⚡ 低延迟响应**: 基于 WebSocket 的实时通信
- **📱 跨平台兼容**: 支持多种编程语言的 WebSocket 库

**模型名称**: `qwen-omni-turbo-realtime`

## 🔧 连接配置

### 基础连接信息

| 配置项 | 说明 |
|--------|------|
| **调用地址** | `wss://dashscope.aliyuncs.com/api-ws/v1/realtime` |
| **查询参数** | `model=qwen-omni-turbo-realtime` |
| **消息头** | `Authorization: Bearer DASHSCOPE_API_KEY` |
| **协议** | WebSocket |

### 快速连接示例

```python
import json
import websocket
import os

API_KEY = os.getenv("DASHSCOPE_API_KEY")
API_URL = "wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=qwen-omni-turbo-realtime"

headers = [
    "Authorization: Bearer " + API_KEY
]

def on_open(ws):
    print(f"Connected to server: {API_URL}")

def on_message(ws, message):
    data = json.loads(message)
    print("Received event:", json.dumps(data, indent=2))

def on_error(ws, error):
    print("Error:", error)

ws = websocket.WebSocketApp(
    API_URL,
    header=headers,
    on_open=on_open,
    on_message=on_message,
    on_error=on_error
)

ws.run_forever()
```

### 会话创建响应

连接成功后将收到 `session.created` 事件：

```json
{
    "event_id": "event_Bj1ixvmDpFda3ahThkOSz",
    "type": "session.created",
    "session": {
        "object": "realtime.session",
        "model": "qwen-omni-turbo-realtime",
        "modalities": ["text", "audio"],
        "voice": "Chelsie",
        "input_audio_format": "pcm16",
        "output_audio_format": "pcm16",
        "input_audio_transcription": {
            "model": "gummy-realtime-v1"
        },
        "turn_detection": {
            "type": "server_vad",
            "threshold": 0.5,
            "prefix_padding_ms": 300,
            "silence_duration_ms": 800,
            "create_response": true,
            "interrupt_response": true
        },
        "temperature": 1.5,
        "max_response_output_tokens": "inf",
        "id": "sess_JSqGSEx9RRfReAIWNegSp"
    }
}
```

## 🎮 交互模式

### 1. VAD 模式 (Voice Activity Detection)

**自动语音检测模式**，适用于语音通话场景：

```python
# 配置 VAD 模式
await client.update_session({
    "modalities": ["text", "audio"],
    "voice": "Chelsie",
    "turn_detection": {
        "type": "server_vad",
        "threshold": 0.1,
        "prefix_padding_ms": 500,
        "silence_duration_ms": 900
    }
})
```

**特点**:
- ✅ 自动检测语音起止
- ✅ 无需手动控制
- ✅ 适合连续对话

### 2. Manual 模式 (手动控制)

**按下即说模式**，需要客户端主动控制：

```python
# 配置手动模式
await client.update_session({
    "modalities": ["text", "audio"],
    "voice": "Chelsie",
    "turn_detection": None
})
```

**特点**:
- 🎛️ 完全客户端控制
- 🎯 精确控制发送时机
- 📱 适合按键交互场景

## 📨 客户端事件

### session.update

更新会话配置，建议连接后立即发送：

```json
{
    "event_id": "event_123",
    "type": "session.update",
    "session": {
        "voice": "Ethan",
        "modalities": ["text", "audio"],
        "input_audio_format": "pcm16",
        "output_audio_format": "pcm16",
        "turn_detection": {
            "type": "server_vad",
            "threshold": 0.2,
            "silence_duration_ms": 800
        }
    }
}
```

**关键参数**:

| 参数 | 类型 | 说明 |
|------|------|------|
| `voice` | string | 音色: "Chelsie", "Serena", "Ethan", "Cherry" |
| `modalities` | array | 模态: ["text"] 或 ["text", "audio"] |
| `input_audio_format` | string | 输入音频格式: "pcm16" |
| `output_audio_format` | string | 输出音频格式: "pcm16" |
| `turn_detection.threshold` | float | VAD阈值: [-1.0, 1.0] |
| `turn_detection.silence_duration_ms` | int | 静音检测时长: [200, 6000]ms |

### input_audio_buffer.append

向音频缓冲区追加音频数据：

```json
{
    "event_id": "event_456",
    "type": "input_audio_buffer.append",
    "audio": "base64_encoded_audio_data"
}
```

### input_audio_buffer.commit

提交音频缓冲区（仅在 Manual 模式需要）：

```json
{
    "event_id": "event_789",
    "type": "input_audio_buffer.commit"
}
```

### response.create

请求生成回复（仅在 Manual 模式需要）：

```json
{
    "event_id": "event_abc",
    "type": "response.create",
    "response": {
        "instructions": "You are a helpful assistant.",
        "modalities": ["text", "audio"]
    }
}
```

### response.cancel

取消当前回复：

```json
{
    "event_id": "event_def",
    "type": "response.cancel"
}
```

## 📤 服务端事件

### 连接管理事件

#### session.created
```json
{
    "type": "session.created",
    "session": { /* 会话配置 */ }
}
```

#### session.updated
```json
{
    "type": "session.updated", 
    "session": { /* 更新后的会话配置 */ }
}
```

#### error
```json
{
    "type": "error",
    "error": {
        "type": "invalid_request_error",
        "code": "invalid_value",
        "message": "错误描述",
        "param": "相关参数"
    }
}
```

### 音频输入事件

#### input_audio_buffer.speech_started
VAD 检测到语音开始：

```json
{
    "type": "input_audio_buffer.speech_started",
    "audio_start_ms": 2022,
    "item_id": "item_Fu4bF8iduL8nfJVsbKb3L"
}
```

#### input_audio_buffer.speech_stopped
VAD 检测到语音结束：

```json
{
    "type": "input_audio_buffer.speech_stopped",
    "audio_end_ms": 2823,
    "item_id": "item_Fu4bF8iduL8nfJVsbKb3L"
}
```

#### input_audio_buffer.committed
音频缓冲区已提交：

```json
{
    "type": "input_audio_buffer.committed",
    "item_id": "item_Fu4bF8iduL8nfJVsbKb3L"
}
```

### 对话管理事件

#### conversation.item.created
对话项创建：

```json
{
    "type": "conversation.item.created",
    "item": {
        "id": "item_Dm13TFlfnCx9IrosLFyeX",
        "object": "realtime.item",
        "type": "message",
        "status": "in_progress",
        "role": "assistant",
        "content": [{"type": "input_audio"}]
    }
}
```

#### conversation.item.input_audio_transcription.completed
输入音频转录完成：

```json
{
    "type": "conversation.item.input_audio_transcription.completed",
    "item_id": "item_Fu4bF8iduL8nfJVsbKb3L",
    "content_index": 0,
    "transcript": "你好，现在几点了？"
}
```

### 响应生成事件

#### response.created
开始生成响应：

```json
{
    "type": "response.created",
    "response": {
        "id": "resp_Vb3496XSAdbX732ybCL17",
        "status": "in_progress",
        "modalities": ["text", "audio"],
        "voice": "Chelsie"
    }
}
```

#### response.output_item.added
新增输出项：

```json
{
    "type": "response.output_item.added",
    "response_id": "resp_P79OOMs8LnrXVpiIHUCKR",
    "output_index": 0,
    "item": {
        "id": "item_OFaPGtzfWCPyGzxnuEX9i",
        "type": "message",
        "status": "in_progress",
        "role": "assistant",
        "content": []
    }
}
```

#### response.audio.delta
音频流数据：

```json
{
    "type": "response.audio.delta",
    "response_id": "resp_P79OOMs8LnrXVpiIHUCKR",
    "item_id": "item_OFaPGtzfWCPyGzxnuEX9i",
    "output_index": 0,
    "content_index": 0,
    "delta": "base64_encoded_audio_chunk"
}
```

#### response.audio_transcript.delta
音频转录文本：

```json
{
    "type": "response.audio_transcript.delta",
    "response_id": "resp_P79OOMs8LnrXVpiIHUCKR",
    "item_id": "item_OFaPGtzfWCPyGzxnuEX9i",
    "output_index": 0,
    "content_index": 0,
    "delta": "你好"
}
```

#### response.done
响应生成完成：

```json
{
    "type": "response.done",
    "response": {
        "id": "resp_Vb3496XSAdbX732ybCL17",
        "status": "completed",
        "output": [
            {
                "id": "item_Ln9VTSx895BCbxuSKWmtg",
                "type": "message",
                "status": "completed",
                "role": "assistant",
                "content": [
                    {
                        "type": "audio",
                        "transcript": "你好，我是通义千问，有什么可以帮助你的吗？"
                    }
                ]
            }
        ],
        "usage": {
            "total_tokens": 261,
            "input_tokens": 127,
            "output_tokens": 134
        }
    }
}
```

## 🔄 交互流程

### VAD 模式流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器
    participant AI as Qwen-Omni

    C->>S: 连接 WebSocket
    S->>C: session.created
    C->>S: session.update (enable VAD)
    S->>C: session.updated
    
    loop 音频输入
        C->>S: input_audio_buffer.append
        S->>C: input_audio_buffer.speech_started
        C->>S: input_audio_buffer.append (继续)
        S->>C: input_audio_buffer.speech_stopped
        S->>C: input_audio_buffer.committed
        S->>C: conversation.item.created
    end
    
    loop AI 响应
        S->>C: response.created
        S->>C: response.output_item.added
        S->>C: response.audio.delta (多次)
        S->>C: response.audio_transcript.delta (多次)
        S->>C: response.done
    end
```

### Manual 模式流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器

    C->>S: 连接 WebSocket
    S->>C: session.created
    C->>S: session.update (disable VAD)
    S->>C: session.updated
    
    loop 用户控制的对话
        C->>S: input_audio_buffer.append (多次)
        C->>S: input_audio_buffer.commit
        S->>C: input_audio_buffer.committed
        S->>C: conversation.item.created
        C->>S: response.create
        S->>C: response.created
        S->>C: response.audio.delta (多次)
        S->>C: response.done
    end
```

## 💻 完整示例代码

### OmniRealtimeClient 客户端类

```python
# omni_realtime_client.py
import asyncio
import websockets
import json
import base64
import time
from typing import Optional, Callable, Dict, Any
from enum import Enum

class TurnDetectionMode(Enum):
    SERVER_VAD = "server_vad"
    MANUAL = "manual"

class OmniRealtimeClient:
    def __init__(
        self,
        base_url: str,
        api_key: str,
        model: str = "qwen-omni-turbo-realtime",
        voice: str = "Ethan",
        turn_detection_mode: TurnDetectionMode = TurnDetectionMode.MANUAL,
        on_text_delta: Optional[Callable[[str], None]] = None,
        on_audio_delta: Optional[Callable[[bytes], None]] = None,
        on_interrupt: Optional[Callable[[], None]] = None,
        on_input_transcript: Optional[Callable[[str], None]] = None,
        on_output_transcript: Optional[Callable[[str], None]] = None
    ):
        self.base_url = base_url
        self.api_key = api_key
        self.model = model
        self.voice = voice
        self.ws = None
        self.turn_detection_mode = turn_detection_mode
        
        # 回调函数
        self.on_text_delta = on_text_delta
        self.on_audio_delta = on_audio_delta
        self.on_interrupt = on_interrupt
        self.on_input_transcript = on_input_transcript
        self.on_output_transcript = on_output_transcript
        
        # 状态管理
        self._current_response_id = None
        self._current_item_id = None
        self._is_responding = False

    async def connect(self) -> None:
        """建立 WebSocket 连接"""
        url = f"{self.base_url}?model={self.model}"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        self.ws = await websockets.connect(url, additional_headers=headers)
        
        # 配置会话
        session_config = {
            "modalities": ["text", "audio"],
            "voice": self.voice,
            "input_audio_format": "pcm16",
            "output_audio_format": "pcm16",
            "input_audio_transcription": {"model": "gummy-realtime-v1"}
        }
        
        if self.turn_detection_mode == TurnDetectionMode.SERVER_VAD:
            session_config["turn_detection"] = {
                "type": "server_vad",
                "threshold": 0.1,
                "prefix_padding_ms": 500,
                "silence_duration_ms": 900
            }
        else:
            session_config["turn_detection"] = None
            
        await self.update_session(session_config)

    async def send_event(self, event: dict) -> None:
        """发送事件到服务器"""
        event['event_id'] = f"event_{int(time.time() * 1000)}"
        await self.ws.send(json.dumps(event))

    async def update_session(self, config: Dict[str, Any]) -> None:
        """更新会话配置"""
        event = {
            "type": "session.update",
            "session": config
        }
        await self.send_event(event)

    async def stream_audio(self, audio_chunk: bytes) -> None:
        """流式发送音频数据 (16bit 16kHz PCM)"""
        audio_b64 = base64.b64encode(audio_chunk).decode()
        event = {
            "type": "input_audio_buffer.append",
            "audio": audio_b64
        }
        await self.send_event(event)

    async def commit_audio_buffer(self) -> None:
        """提交音频缓冲区 (Manual 模式)"""
        event = {"type": "input_audio_buffer.commit"}
        await self.send_event(event)

    async def create_response(self) -> None:
        """请求生成回复 (Manual 模式)"""
        event = {
            "type": "response.create",
            "response": {
                "instructions": "You are a helpful assistant.",
                "modalities": ["text", "audio"]
            }
        }
        await self.send_event(event)

    async def cancel_response(self) -> None:
        """取消当前回复"""
        event = {"type": "response.cancel"}
        await self.send_event(event)

    async def handle_messages(self) -> None:
        """处理服务器消息"""
        try:
            async for message in self.ws:
                event = json.loads(message)
                event_type = event.get("type")
                
                if event_type == "error":
                    print(f"❌ Error: {event['error']}")
                    
                elif event_type == "response.created":
                    self._current_response_id = event.get("response", {}).get("id")
                    self._is_responding = True
                    
                elif event_type == "response.done":
                    self._is_responding = False
                    self._current_response_id = None
                    
                elif event_type == "input_audio_buffer.speech_started":
                    if self._is_responding and self.on_interrupt:
                        await self.cancel_response()
                        self.on_interrupt()
                        
                elif event_type == "response.audio.delta":
                    if self.on_audio_delta:
                        audio_bytes = base64.b64decode(event["delta"])
                        self.on_audio_delta(audio_bytes)
                        
                elif event_type == "response.audio_transcript.delta":
                    if self.on_output_transcript:
                        self.on_output_transcript(event.get("delta", ""))
                        
                elif event_type == "conversation.item.input_audio_transcription.completed":
                    if self.on_input_transcript:
                        self.on_input_transcript(event.get("transcript", ""))
                        
        except websockets.exceptions.ConnectionClosed:
            print("🔌 Connection closed")
        except Exception as e:
            print(f"❌ Error in message handling: {e}")

    async def close(self) -> None:
        """关闭连接"""
        if self.ws:
            await self.ws.close()
```

### VAD 模式示例

```python
# vad_mode_example.py
import os
import asyncio
import pyaudio
import queue
import threading
from omni_realtime_client import OmniRealtimeClient, TurnDetectionMode

# 音频配置
RATE = 24000
CHUNK = 3200
FORMAT = pyaudio.paInt16
CHANNELS = 1

# 全局音频队列
audio_queue = queue.Queue()
interrupt_flag = threading.Event()

def handle_audio_output(audio_data):
    """处理输出音频"""
    audio_queue.put(audio_data)

def handle_interrupt():
    """处理中断 - 停止播放"""
    interrupt_flag.set()
    with audio_queue.mutex:
        audio_queue.queue.clear()

def audio_player_thread():
    """音频播放线程"""
    p = pyaudio.PyAudio()
    stream = p.open(
        format=FORMAT,
        channels=CHANNELS, 
        rate=RATE,
        output=True,
        frames_per_buffer=CHUNK
    )
    
    try:
        while True:
            if interrupt_flag.is_set():
                interrupt_flag.clear()
                continue
                
            try:
                audio_data = audio_queue.get(timeout=0.5)
                if audio_data is None:
                    break
                stream.write(audio_data)
                audio_queue.task_done()
            except queue.Empty:
                continue
    finally:
        stream.close()
        p.terminate()

async def microphone_stream(client):
    """麦克风录音流"""
    p = pyaudio.PyAudio()
    stream = p.open(
        format=FORMAT,
        channels=CHANNELS,
        rate=RATE,
        input=True,
        frames_per_buffer=CHUNK
    )
    
    try:
        while True:
            audio_data = stream.read(CHUNK)
            await client.stream_audio(audio_data)
            await asyncio.sleep(0.05)
    finally:
        stream.close()
        p.terminate()

async def main():
    # 启动音频播放线程
    player = threading.Thread(target=audio_player_thread, daemon=True)
    player.start()
    
    # 创建客户端
    client = OmniRealtimeClient(
        base_url="wss://dashscope.aliyuncs.com/api-ws/v1/realtime",
        api_key=os.environ.get("DASHSCOPE_API_KEY"),
        voice="Chelsie",
        turn_detection_mode=TurnDetectionMode.SERVER_VAD,
        on_audio_delta=handle_audio_output,
        on_interrupt=handle_interrupt,
        on_input_transcript=lambda text: print(f"👤 用户: {text}"),
        on_output_transcript=lambda text: print(f"🤖 助手: {text}", end="", flush=True)
    )
    
    try:
        await client.connect()
        print("🎤 开始录音，请说话...")
        
        # 并行运行消息处理和录音
        await asyncio.gather(
            client.handle_messages(),
            microphone_stream(client)
        )
    finally:
        audio_queue.put(None)  # 停止播放线程
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🎵 音色选择

Qwen-Omni 支持四种不同音色：

| 音色名称 | 特点 | 适用场景 |
|----------|------|----------|
| **Chelsie** | 温暖友好的女声 | 客服、教育、日常对话 |
| **Serena** | 专业优雅的女声 | 商务、播报、正式场合 |
| **Ethan** | 稳重清晰的男声 | 技术支持、培训、解说 |
| **Cherry** | 活泼可爱的女声 | 娱乐、游戏、儿童教育 |

```python
# 设置不同音色
await client.update_session({"voice": "Ethan"})  # 切换到男声
await client.update_session({"voice": "Cherry"}) # 切换到活泼女声
```

## ⚙️ 高级配置

### VAD 参数调优

```python
# 适用于安静环境
vad_config = {
    "type": "server_vad",
    "threshold": -0.3,          # 更敏感
    "silence_duration_ms": 600   # 更短等待
}

# 适用于嘈杂环境  
vad_config = {
    "type": "server_vad", 
    "threshold": 0.5,           # 不太敏感
    "silence_duration_ms": 1200  # 更长等待
}

# 适用于快速对话
vad_config = {
    "type": "server_vad",
    "threshold": 0.0,
    "silence_duration_ms": 400   # 快速响应
}
```

### 音频格式要求

| 参数 | 值 | 说明 |
|------|---|------|
| **采样率** | 24000 Hz | 固定值，不可更改 |
| **位深度** | 16 bit | PCM 格式 |
| **声道数** | 1 (单声道) | 不支持立体声 |
| **编码格式** | PCM16 | 线性脉冲编码调制 |
| **字节序** | Little Endian | 小端字节序 |

### 性能优化建议

```python
# 1. 音频分块大小优化
OPTIMAL_CHUNK_SIZE = 3200  # 约133ms的音频

# 2. 并发处理
async def optimized_audio_handling():
    tasks = [
        client.handle_messages(),      # 消息处理
        microphone_stream(client),     # 录音
        audio_playback_handler(),      # 播放
        connection_monitor()           # 连接监控
    ]
    await asyncio.gather(*tasks)

# 3. 缓冲区管理
audio_buffer = asyncio.Queue(maxsize=50)  # 限制缓冲区大小

# 4. 错误恢复
async def connection_with_retry():
    max_retries = 3
    for attempt in range(max_retries):
        try:
            await client.connect()
            break
        except Exception as e:
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
            else:
                raise
```

## 🚨 错误处理

### 常见错误类型

| 错误码 | 错误类型 | 原因 | 解决方案 |
|--------|----------|------|----------|
| `invalid_value` | 参数错误 | 配置参数不合法 | 检查参数范围和类型 |
| `auth_error` | 认证错误 | API Key 无效 | 验证 DASHSCOPE_API_KEY |
| `rate_limit` | 限流错误 | 请求频率过高 | 实现退避重试机制 |
| `connection_error` | 连接错误 | 网络问题 | 检查网络连接状态 |

### 错误处理示例

```python
async def robust_event_handler(client):
    retry_count = 0
    max_retries = 3
    
    while retry_count < max_retries:
        try:
            async for message in client.ws:
                event = json.loads(message)
                
                if event.get("type") == "error":
                    error = event["error"]
                    error_type = error.get("type")
                    
                    if error_type == "rate_limit":
                        # 限流错误 - 等待重试
                        await asyncio.sleep(5)
                        continue
                    elif error_type == "auth_error":
                        # 认证错误 - 无法恢复
                        raise Exception(f"认证失败: {error}")
                    else:
                        # 其他错误 - 记录并继续
                        print(f"⚠️ 警告: {error}")
                        
                # 处理正常事件...
                        
        except websockets.exceptions.ConnectionClosed:
            retry_count += 1
            if retry_count < max_retries:
                print(f"🔄 连接断开，重试 {retry_count}/{max_retries}")
                await asyncio.sleep(2 ** retry_count)
                await client.connect()
            else:
                raise Exception("连接重试次数已达上限")
```

## 📊 最佳实践

### ✅ 推荐做法

1. **🔧 合理配置 VAD 参数**
   ```python
   # 根据环境调整阈值
   threshold = -0.2 if is_quiet_environment else 0.3
   ```

2. **🎵 优化音频处理**
   ```python
   # 使用适当的缓冲区大小
   CHUNK_SIZE = 3200  # 约133ms
   
   # 非阻塞音频处理
   await asyncio.sleep(0.001)  # 让出控制权
   ```

3. **📊 监控连接状态**
   ```python
   # 定期检查连接健康度
   async def health_check():
       while True:
           if client.ws.closed:
               await reconnect()
           await asyncio.sleep(30)
   ```

4. **🛡️ 实现错误恢复**
   ```python
   # 指数退避重试
   for attempt in range(max_retries):
       try:
           await client.connect()
           break
       except Exception:
           await asyncio.sleep(2 ** attempt)
   ```

### ❌ 避免事项

1. **❌ 不要发送不合规的音频格式**
   ```python
   # 错误：发送立体声或其他采样率
   # 正确：确保 16bit 24kHz 单声道 PCM
   ```

2. **❌ 不要忽略错误事件**
   ```python
   # 错误：不处理 error 事件
   # 正确：总是检查和处理错误
   ```

3. **❌ 不要在 VAD 模式下手动控制**
   ```python
   # 错误：VAD 模式下调用 commit_audio_buffer()
   # 正确：让服务器自动处理
   ```

4. **❌ 不要阻塞事件循环**
   ```python
   # 错误：同步音频处理
   # 正确：使用异步处理
   ```

### 🔍 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 事件监控
def log_event(event):
    event_type = event.get("type", "unknown")
    print(f"📡 {event_type}: {json.dumps(event, indent=2)}")

# 音频数据监控
def log_audio_stats(audio_data):
    print(f"🎵 Audio: {len(audio_data)} bytes, "
          f"duration: {len(audio_data)/48000:.2f}s")

# 连接状态监控
async def connection_monitor():
    while True:
        status = "connected" if client.ws and not client.ws.closed else "disconnected"
        print(f"🔌 Connection: {status}")
        await asyncio.sleep(10)
```

## 📚 参考资源

- **🌐 官方文档**: [阿里云百炼平台](https://dashscope.aliyuncs.com/)
- **🔗 WebSocket 标准**: [RFC 6455](https://tools.ietf.org/html/rfc6455)
- **🎵 PyAudio 文档**: [PyAudio Documentation](https://people.csail.mit.edu/hubert/pyaudio/)
- **⚡ AsyncIO 指南**: [Python AsyncIO](https://docs.python.org/3/library/asyncio.html)

---

通过本参考文档，你可以快速集成 Qwen-Omni 实时 API，实现高质量的语音对话应用。如有问题，请参考错误处理章节或查看详细的事件日志。 