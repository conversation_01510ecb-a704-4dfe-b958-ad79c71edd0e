"""
Listener Agent - 负责接收和处理用户音频输入

使用阿里千问qwen-omni-turbo-realtime API进行实时语音识别，
保持与原有接口的完全兼容性。
"""

import asyncio
import json
import base64
from loguru import logger
from typing import Optional, Dict, Any, Callable

from core.base_agent import BaseAgent
from core.realtime_manager import RealtimeManager
from core.client_factory import ClientFactory
from core.config import get_realtime_config

class ListenerAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="Listener")
        self.realtime_manager: Optional[RealtimeManager] = None
        self.is_listening = False
        self.audio_callback: Optional[Callable] = None
        self.transcript_callback: Optional[Callable] = None
        self.current_session_id: Optional[str] = None  # 添加session_id跟踪
        self.is_shielded = False  # 🎯 新增护盾状态
        
        # 向后兼容性：保持原有的音频缓冲区接口
        self.audio_buffer = []
        self.buffer_size = 4096
        
    async def initialize(self):
        """初始化Listener，使用全局的RealtimeManager实例"""
        try:
            # 🎯 修复：使用全局的RealtimeManager实例而不是创建新实例
            from core.realtime_manager import get_global_manager
            
            self.realtime_manager = get_global_manager()
            if not self.realtime_manager:
                raise Exception("Global RealtimeManager not available. Please ensure it's initialized first.")
            
            logger.info(f"✅ ListenerAgent使用全局RealtimeManager实例")
            
            # 注册事件处理器（如果还没有注册的话）
            # 由于使用全局实例，这些处理器可能已经注册了，需要检查避免重复
            
            # 检查是否已经连接
            if not self.realtime_manager.is_connected:
                logger.warning("⚠️ Global RealtimeManager not connected. Listener will wait for connection.")
            
            # --- START OF FIX ---
            # 订阅系统命令
            from core.message_bus import message_bus, TopicNames
            await message_bus.subscribe(TopicNames.SYSTEM_COMMANDS, self.handle_system_command)
            # --- END OF FIX ---
            
            logger.info("ListenerAgent initialized with global RealtimeManager")
            return True
                
        except Exception as e:
            logger.error(f"Failed to initialize ListenerAgent: {e}")
            raise
    
    async def start(self) -> bool:
        """
        启动ListenerAgent代理
        
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info("Starting ListenerAgent...")
            
            # 确保已经初始化
            if not self.realtime_manager or not self.realtime_manager.is_connected:
                await self.initialize()
            
            # 开始监听（如果之前未开始）
            if not self.is_listening:
                await self.start_listening()
            
            # 设置运行状态标志
            self.is_running = True
            
            logger.info("ListenerAgent started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start ListenerAgent: {e}")
            self.is_running = False
            return False
    
    async def shutdown(self) -> None:
        """
        关闭ListenerAgent代理，清理资源
        """
        try:
            logger.info("Shutting down ListenerAgent...")
            
            # 停止监听
            await self.stop_listening()
            
            # 断开连接和清理资源
            await self.cleanup()
            
            logger.info("ListenerAgent shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during ListenerAgent shutdown: {e}")
    
    async def start_listening(self, audio_callback: Optional[Callable] = None, 
                            transcript_callback: Optional[Callable] = None):
        """
        开始监听音频输入（保持向后兼容接口）
        
        Args:
            audio_callback: 音频数据回调函数
            transcript_callback: 转录结果回调函数
        """
        if not self.realtime_manager or not self.realtime_manager.is_connected:
            await self.initialize()
            
        self.audio_callback = audio_callback
        self.transcript_callback = transcript_callback
        self.is_listening = True
        
        logger.info("Started listening for audio input")
        
    async def stop_listening(self):
        """停止监听音频输入"""
        self.is_listening = False
        self.audio_callback = None
        self.transcript_callback = None
        
        logger.info("Stopped listening for audio input")
    
    async def start_session(self, session_id: str) -> bool:
        """
        启动会话级别的音频监听
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info(f"启动Listener会话: {session_id}")
            
            # 存储当前会话ID用于转录消息
            self.current_session_id = session_id
            
            # 🎯 修复：设置realtime_manager的session_id
            if self.realtime_manager:
                self.realtime_manager.set_current_session_id(session_id)
                logger.debug(f"✅ 已设置RealtimeManager会话ID: {session_id}")
            
            # 确保已经初始化和启动
            if not self.realtime_manager or not self.realtime_manager.is_connected:
                await self.initialize()
            
            if not self.is_listening:
                await self.start_listening()
            
            # 会话级别的配置可以在这里添加
            # 例如：会话特定的音频参数、回调设置等
            
            logger.info(f"✅ Listener会话启动成功: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动Listener会话失败 {session_id}: {e}")
            return False

    async def stop_session(self):
        """停止当前会话"""
        # 🎯 修复：清除realtime_manager的session_id
        if self.realtime_manager:
            self.realtime_manager.clear_current_session_id()
            logger.debug("✅ 已清除RealtimeManager会话ID")
        
        self.current_session_id = None
        logger.info("Listener会话已停止")

    async def handle_system_command(self, topic: str, message_data: Dict[str, Any]):
        """处理来自Orchestrator的系统命令"""
        from core.event_types import EventType
        
        event_type = message_data.get("event_type")
        data = message_data.get("data", {})
        session_id = data.get("session_id")

        if self.current_session_id != session_id:
            return

        if event_type == EventType.LISTENER_PAUSE.value:
            self.is_shielded = True
            logger.info("🛡️ Listener audio processing paused by orchestrator.")
        elif event_type == EventType.LISTENER_RESUME.value:
            self.is_shielded = False
            logger.info("✅ Listener audio processing resumed.")

    async def process_audio_chunk(self, audio_data: bytes):
        """
        处理音频数据块（保持向后兼容接口）
        
        Args:
            audio_data: PCM16格式的音频数据
        """
        # --- START OF FIX ---
        if self.is_shielded:
            logger.debug("🛡️ Audio chunk ignored due to active shield.")
            return
        # --- END OF FIX ---
        
        if not self.is_listening or not self.realtime_manager:
            return
            
        try:
            # 向后兼容：添加到音频缓冲区
            self.audio_buffer.append(audio_data)
            if len(self.audio_buffer) > self.buffer_size:
                self.audio_buffer.pop(0)
            
            # 使用千问API处理音频
            success = await self.realtime_manager.send_audio_buffer_append(audio_data)
            
            if success and self.audio_callback:
                # 保持向后兼容的回调接口
                await self.audio_callback(audio_data)
                
        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
    
    async def commit_audio(self):
        """
        提交音频缓冲区进行处理（新增千问特有功能）
        """
        if not self.realtime_manager:
            return False
            
        try:
            return await self.realtime_manager.commit_audio_buffer()
        except Exception as e:
            logger.error(f"Error committing audio: {e}")
            return False
    
    async def _handle_transcription_completed(self, event_type: str, data: Dict[str, Any]):
        """处理转录完成事件"""
        try:
            transcript = data.get("transcript", "")
            item_id = data.get("item_id", "")
            
            logger.info(f"Transcription completed: '{transcript}'")
            
            # 发布到消息总线 - 修复：添加session_id
            if transcript:
                from core.message_bus import message_bus, TopicNames, create_message
                from core.event_types import EventType, MessageType
                
                message_data = create_message(
                    event_type=EventType.TRANSCRIPT_COMPLETED,
                    message_type=MessageType.LISTENER_TRANSCRIPT,
                    data={
                        "text": transcript,
                        "transcript": transcript,
                        "item_id": item_id,
                        "session_id": getattr(self, 'current_session_id', None),  # 添加session_id
                        "timestamp": data.get("timestamp"),
                        "confidence": 1.0,
                        "is_final": True
                    },
                    source_agent="listener"
                )
                
                await message_bus.publish(TopicNames.TRANSCRIPT_COMPLETED, message_data)
                logger.debug(f"📡 用户转录已发布到消息总线: '{transcript}' (会话: {getattr(self, 'current_session_id', 'None')})")
            
            # 触发向后兼容的回调
            if self.transcript_callback:
                await self.transcript_callback({
                    "text": transcript,
                    "item_id": item_id,
                    "timestamp": data.get("timestamp"),
                    "confidence": 1.0  # 千问API暂无置信度，设为1.0
                })
                
        except Exception as e:
            logger.error(f"Error handling transcription: {e}")
    
    async def _handle_speech_started(self, event_type: str, data: Dict[str, Any]):
        """处理语音开始事件"""
        logger.debug("Speech started detected by VAD")
        
        # 向消息总线发送事件（保持向后兼容）
        await self.message_bus.publish("speech.started", {
            "agent": self.name,
            "timestamp": data.get("local_timestamp"),
            "vad_data": data
        })
    
    async def _handle_speech_stopped(self, event_type: str, data: Dict[str, Any]):
        """处理语音停止事件"""  
        logger.debug("Speech stopped detected by VAD")
        
        # 向消息总线发送事件（保持向后兼容）
        await self.message_bus.publish("speech.stopped", {
            "agent": self.name, 
            "timestamp": data.get("local_timestamp"),
            "vad_data": data
        })
    
    async def get_recent_transcript(self) -> Optional[str]:
        """
        获取最近的转录结果（保持向后兼容接口）
        
        Returns:
            Optional[str]: 最近的转录文本
        """
        # 这个方法保持向后兼容，但千问API是事件驱动的
        # 实际转录结果通过回调函数提供
        logger.warning("get_recent_transcript is deprecated with Qwen API. Use transcript_callback instead.")
        return None
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop_listening()
            
            if self.realtime_manager:
                await self.realtime_manager.disconnect()
                self.realtime_manager = None
                
            self.audio_buffer.clear()
            logger.info("ListenerAgent cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during ListenerAgent cleanup: {e}") 

# 向后兼容别名 - 保持与现有代码的兼容性
Listener = ListenerAgent 