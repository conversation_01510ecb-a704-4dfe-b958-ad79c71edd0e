#!/bin/bash

# Audio Agent Docker Start Script
echo "🚀 Starting Audio Agent System in Docker..."

# Create log directory if it doesn't exist
mkdir -p /app/logs

# Check if .env file exists, if not create from example
if [ ! -f /app/.env ]; then
    echo "⚠️  .env file not found, copying from .env.example"
    cp /app/config.env.example /app/.env
fi

# Start supervisor which will handle Redis and the application
echo "🎯 Starting Audio Agent with Supervisor..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf 