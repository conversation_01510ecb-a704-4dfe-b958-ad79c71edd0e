
## **主动式实时 AI 对话助手 - 前端 API 对接文档 (v2.1)**

### **1. 概述**

本文档定义了前端客户端与“主动式实时 AI 对话助手”后端服务之间通过 WebSocket 进行实时通信的接口协议。前端的核心职责是：

- 建立和维护一个稳定的 WebSocket 连接。
- 采集用户的麦克风音频数据（PCM 格式），通过 WebSocket 发送给后端。
- 接收并处理后端推送的各类实时事件，如实时转录、AI 回复、音频播放指令等，并更新 UI。
- 管理对话的启动、模式选择和结束。

### **2. WebSocket 连接**

#### **2.1. 连接端点 (Endpoint)**

- **URL**: `ws://<your_server_host>:<ws_port>/ws/conversation`
- **示例**: `ws://localhost:8000/ws/conversation`

连接建立后，后端会为本次连接创建一个唯一的 `session_id`，并在后续的大部分消息中携带此 ID。

#### **2.2. 心跳机制 (Heartbeat)**

为保持连接活跃并检测断线，前端应实现一个心跳机制。

- **客户端发送**: 定期（推荐每 **20 秒**）向服务器发送一个 `ping` 消息。
- **服务器响应**: 服务器会立即回复一个 `pong` 消息。
- **断线检测**: 如果在合理的时间内（如连续 2 次未收到`pong`响应）没有收到服务器的 `pong` 回复，前端应认为连接已断开，并尝试重新连接。

### **3. 消息协议**

所有在 WebSocket 上传输的消息都必须是 **JSON 格式的字符串**。

#### **3.1. 消息基本结构**

每个消息都包含一个 `type` 字段，用于标识消息的类型。

**客户端 -\> 服务器 (C2S)**

```json
{
  "type": "message_type_name",
  "data": { ... } // 消息负载
}
```

**服务器 -\> 客户端 (S2C)**

```json
{
  "type": "message_type_name"
  // ... 其他字段
}
```

---

### **4. 客户端 -\> 服务器 (C2S) 消息详解**

#### **4.1. `ping`**

- **描述**: 客户端心跳消息，用于保持连接活跃。
- **负载 (`data`)**:
  ```json
  {
    "timestamp": "ISO_8601_format_string" // 客户端当前时间戳
  }
  ```
- **示例**:
  ```json
  {
    "type": "ping",
    "data": {
      "timestamp": "2025-07-24T15:30:00.123Z"
    }
  }
  ```

#### **4.2. `mode_selection`**

- **描述**: **这是开始一个新对话的核心消息**。在点击“连接”并成功后，前端首先应让用户选择模式，然后发送此消息来正式启动一个完整的对话会话。
- **负载 (`data`)**:
  ```json
  {
    "mode": "standard" | "interviewer"
  }
  ```
- **示例**: 选择面试官模式
  ```json
  {
    "type": "mode_selection",
    "data": {
      "mode": "interviewer"
    }
  }
  ```

#### **4.3. `audio_input`**

- **描述**: 实时发送用户的麦克风音频数据。前端需要将原始的 PCM 音频数据进行 **Base64 编码** 后发送。
- **音频格式要求**:
  - **采样率 (Sample Rate)**: 24000 Hz
  - **位深 (Bit Depth)**: 16-bit
  - **声道 (Channels)**: 单声道 (Mono)
- **负载 (`data`)**:
  ```json
  {
    "data": "base64_encoded_pcm_audio_data_string"
  }
  ```
- **示例**:
  ```json
  {
    "type": "audio_input",
    "data": {
      "data": "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="
    }
  }
  ```
- **发送时机**: 用户点击“开始录音”后，持续、高频地（例如每 100ms）发送音频流。

#### **4.4. `end_conversation`**

- **描述**: 用户主动结束当前对话。
- **负载 (`data`)**: 无
- **示例**:
  ```json
  {
    "type": "end_conversation",
    "data": {}
  }
  ```

---

### **5. 服务器 -\> 客户端 (S2C) 消息详解**

#### **5.1. `pong`**

- **描述**: 对客户端 `ping` 消息的响应。
- **负载**:
  ```json
  {
    "type": "pong",
    "timestamp": "ISO_8601_format_string", // 服务器当前时间戳
    "original_timestamp": "ISO_8601_format_string" // 从ping消息中收到的客户端时间戳
  }
  ```

#### **5.2. `conversation_started`**

- **描述**: 当服务器成功处理 `mode_selection` 消息后，会返回此消息，表示对话已正式开始。前端收到此消息后，可以更新 UI 状态为“对话中”。
- **负载**:
  ```json
  {
    "type": "conversation_started",
    "session_id": "string", // 本次对话的唯一ID
    "mode": "standard" | "interviewer", // 当前生效的模式
    "timestamp": "ISO_8601_format_string"
  }
  ```

#### **5.3. `transcript_delta`**

- **描述**: **用户的实时语音转写（中间结果）**。服务器会高频推送此消息，用于在 UI 上实时显示用户正在说的话。
- **负载**:
  ```json
  {
    "type": "transcript_delta",
    "text": "string", // 当前增量的转写文本
    "transcript": "string", // 同 text
    "session_id": "string",
    "is_final": false,
    "confidence": 1.0 // 置信度，目前固定为1.0
  }
  ```

#### **5.4. `transcript_complete`**

- **描述**: **用户的完整一句话的语音转写（最终结果）**。当用户停顿，VAD 检测到一句话结束时，服务器会发送此消息。
- **负载**:
  ```json
  {
    "type": "transcript_complete",
    "text": "string", // 完整的句子转写文本
    "transcript": "string", // 同 text
    "session_id": "string",
    "is_final": true,
    "confidence": 1.0
  }
  ```

#### **5.5. `ai_response`**

- **描述**: **AI 的文本回复**。根据后端 `README` 和代码的最新逻辑，此事件同时承载了**合作型打断**和**干扰型打断**的文本内容，以及常规的 AI 回复。
  - **合作型打断** (`Cooperative Interruption`):
    - 根据 `README` 更新，此类打断**不再生成音频**。
    - 服务器会发送一个 `ai_response` 消息，其 `text` 内容为 **emoji** 或 **带方框的短语**，例如 `【确实是👍】` 或 `【没事的】`。
    - 前端应将这类特殊的 `text` 直接渲染在 UI 上，作为一种实时的文字反馈。
  - **干扰型打断** (`Disruptive Interruption`) 和 **常规回复**:
    - `text` 字段包含完整的 AI 回复文本。
    - 此消息通常会**紧跟着 `audio_output` 消息**，因为后端会立即开始合成并播放音频。
- **负载**:
  ```json
  {
    "type": "ai_response",
    "text": "string", // AI的回复文本。可能是完整句子，也可能是合作型打断的特殊格式文本。
    "content": "string", // 同 text
    "session_id": "string",
    "response_id": "string", // 该回复的唯一ID
    "message_key": "string", // 可用于前端去重的唯一键
    "streaming": false, // 对于ai_response，通常为false
    "complete": true // 对于ai_response，通常为true
  }
  ```

#### **5.6. `audio_output`**

- **描述**: **AI 回复的音频数据**。这是实现 AI 语音播放的核心。后端会将合成的 PCM 音频数据进行 Base64 编码后，通过此消息实时流式推送到前端。前端需要立即解码并播放。
- **音频格式**: PCM, 16-bit, 24kHz, 单声道。
- **负载**:
  ```json
  {
    "type": "audio_output",
    "data": "base64_encoded_pcm_audio_data_string", // 音频数据块
    "format": "pcm16",
    "sample_rate": 24000,
    "session_id": "string",
    "timestamp": "ISO_8601_format_string",
    "source": "direct_channel" | "message_bus_fallback" // 音频来源
  }
  ```
- **前端处理逻辑**:
  1.  **建立音频队列**: 创建一个先进先出 (FIFO) 的音频块队列。
  2.  **接收与入队**: 收到 `audio_output` 消息后，将 `data` 字段中的音频块解码并放入队列。
  3.  **实时播放**: 只要队列不为空，就取出音频块并使用 Web Audio API 进行播放。这可以保证音频流畅且按序播放。
  4.  **数据去重**: 根据 `realtime_manager.py` 的设计，后端可能通过主备双通道发送音频。前端应实现一个简单的去重机制（例如，记录最近处理过的几个音频块的哈希值），防止重复播放。

#### **5.7. `cooperative_interjection`**

- **描述**: **合作型文字附和**。这是新架构中 TacticalThinker 生成的实时文字反馈，用于在 UI 上显示 emoji 或简短的支持性文字，如"【确实是 👍】"、"【没事的】"等。这些附和**不包含语音**，仅为文字显示。
- **负载**:
  ```json
  {
    "type": "cooperative_interjection",
    "text": "string", // 附和文字，如"【确实是👍】"、"【我理解】"
    "session_id": "string",
    "timestamp": "ISO_8601_format_string"
  }
  ```
- **前端处理逻辑**:
  - 将附和文字显示在对话界面的适当位置（如消息气泡旁边或状态栏）
  - 可以设置短暂的显示时间（如 3-5 秒后淡出）
  - 建议使用不同的样式来区分这些附和与正常的 AI 回复

#### **5.8. `audio_playback_stop`**

- **描述**: **强制停止音频播放指令**。当发生用户 `barge-in`（用户在 AI 说话时插话）或其他需要立即停止 AI 声音的情况时，服务器会发送此指令。
- **负载**:
  ```json
  {
    "type": "audio_playback_stop",
    "command": "stop",
    "reason": "speech_interrupted" | "manual_stop", // 停止原因
    "session_id": "string"
  }
  ```
- **前端处理逻辑**:
  - 立即停止当前所有正在播放的 Web Audio API 源。
  - 清空音频播放队列。

#### **5.9. `conversation_ended`**

- **描述**: 服务器确认对话已结束。
- **负载**:
  ```json
  {
    "type": "conversation_ended",
    "session_id": "string",
    "timestamp": "ISO_8601_format_string"
  }
  ```

#### **5.10. `error`**

- **描述**: 当处理发生错误时，服务器会发送此消息。
- **负载**:
  ```json
  {
    "type": "error",
    "message": "string" // 错误描述信息
  }
  ```

---

### **6. 完整交互流程示例**

以下是一个典型的“面试官模式”下的交互流程：

1.  **[C-\>S]** **连接**: 前端与 `ws://localhost:8000/ws/conversation` 建立 WebSocket 连接。
2.  **[S-\>C]** **连接确认**: 连接成功。
3.  **[C-\>S]** **模式选择**: 用户选择“面试官模式”。
    ```json
    { "type": "mode_selection", "data": { "mode": "interviewer" } }
    ```
4.  **[S-\>C]** **对话开始**: 服务器确认模式，对话正式开始。
    ```json
    { "type": "conversation_started", "session_id": "xyz-123", "mode": "interviewer", ... }
    ```
5.  **[UI]** 前端显示“对话中”，并启用“开始录音”按钮。
6.  **[C-\>S]** **发送音频**: 用户点击“开始录音”并发言：“你好，我叫张三，我来面试贵公司的 AI 算法工程师岗位，我精通 Python 和 PyTorch，并且……”
    - 前端持续发送 `audio_input` 消息。
7.  **[S-\>C]** **实时转录**: 服务器实时返回用户的转录。 - `{ "type": "transcript_delta", "text": "你好", ... }` - `{ "type": "transcript_delta", "text": "我叫张三", ... }` - ...
    7.5 **[S-\>C]** **合作型附和**: 当用户说到"我精通 Python"时，TacticalThinker 检测到自信表达，发送文字附和。
    `json
{ "type": "cooperative_interjection", "text": "【确实是👍】", "session_id": "xyz-123", ... }
`
    **[前端处理]** 前端在对话界面旁边显示"【确实是 👍】"，3 秒后淡出。
8.  **[S-\>C]** **完整转录**: 用户说完一句话并停顿。
    ```json
    { "type": "transcript_complete", "text": "你好，我叫张三，我来面试贵公司的AI算法工程师岗位，我精通Python和PyTorch，并且……", ... }
    ```
9.  **[AI 处理]** 后端的 `Strategic Thinker` 开始分析这段话，发现用户回答有些冗长，超出了预设时间。`Decider` 决定进行**干扰型打断**。
10. **[S-\>C]** **AI 回复文本**: 服务器立即发送 AI 的打断文本。
    ```json
    { "type": "ai_response", "text": "请简洁一些，重点是什么？", ... }
    ```
11. **[S-\>C]** **AI 音频流**: 几乎同时，服务器开始流式发送上述文本的音频数据。
    - `{ "type": "audio_output", "data": "...", ... }`
    - `{ "type": "audio_output", "data": "...", ... }`
12. **[前端处理]** 前端收到 `ai_response` 后将文本显示在 UI 上，并开始接收 `audio_output` 流，解码并实时播放 “请简洁一些，重点是什么？” 的声音。
13. **[用户 Barge-in]** AI 还在说话时，用户立即插话："好的，我的重点是我的项目经验……"
    13.5 **[S-\>C]** **实时附和**: 用户说"好的"时，TacticalThinker 检测到配合态度，立即发送附和。
    `json
{ "type": "cooperative_interjection", "text": "【嗯嗯】", "session_id": "xyz-123", ... }
`
14. **[C-\>S]** **发送音频**: 前端检测到用户开始说话，立即恢复发送 `audio_input` 消息。
15. **[AI 处理]** 后端的 `Orchestrator` 检测到在 `AI_SPEAKING` 状态下收到了用户输入，判定为 `barge-in`。
16. **[S-\>C]** **停止播放指令**: 服务器立即发送 `audio_playback_stop` 指令。
    ```json
    { "type": "audio_playback_stop", "command": "stop", "reason": "speech_interrupted", ... }
    ```
17. **[前端处理]** 前端收到指令，立即停止播放 AI 的声音，并清空音频队列。
18. **[循环]** 对话继续，回到第 7 步。

### **🎯 新架构的关键体验优势**

上述流程展示了重构后的双流分析架构的核心特性：

1. **合作型附和 (步骤 7.5)**: 用户在说话过程中会看到 AI 的实时文字反馈，如"【确实是 👍】"，提供自然的互动感，但不会打断用户说话。这些附和通过 `cooperative_interjection` 消息类型传递，前端应将其渲染为短暂显示的文字元素。

2. **干扰型打断 (步骤 9-12)**: 当 StrategicThinker 检测到用户过于冗长、重复或偏题时，AI 会主动语音打断，通过传统的 `ai_response` + `audio_output` 消息组合实现。

3. **并行处理**: 两种分析完全并行进行，TacticalThinker 的合作型附和不会影响 StrategicThinker 的干扰型打断判断。

4. **上下文感知**: 两个 Thinker 都维护 20 轮对话历史，确保所有判断都基于完整的对话上下文。

**前端开发要点**：

- 为 `cooperative_interjection` 消息设计专门的 UI 展示区域
- 实现文字附和的淡入淡出效果，建议显示 3-5 秒
- 确保文字附和不影响正常的对话流程和音频播放

---

这份文档应该能覆盖前端开发所需的所有核心细节。祝开发顺利！
