"""
BaseAgent抽象基类for Audio Agent系统
定义所有agents的统一接口和生命周期管理
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from enum import Enum
from .exceptions import AgentInitializationError, AgentStateError
from .event_types import AgentState

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    所有Audio Agent的抽象基类
    定义统一的生命周期管理和接口
    """
    
    def __init__(self, name: str):
        self.name = name
        self.state = AgentState.IDLE
        self.is_running = False
        self.initialization_time = None
        self.error_message = None
        self.logger = logging.getLogger(f"agents.{name}")
    
    @abstractmethod
    async def initialize(self) -> bool:
        """
        初始化agent
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    async def start(self) -> bool:
        """
        启动agent
        
        Returns:
            bool: 启动是否成功
        """
        pass
    
    @abstractmethod
    async def shutdown(self) -> None:
        """
        关闭agent，清理资源
        """
        pass
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """
        获取agent状态信息
        
        Returns:
            Dict[str, Any]: agent状态信息
        """
        return {
            "name": self.name,
            "state": self.state.value,
            "is_running": self.is_running,
            "initialization_time": self.initialization_time,
            "error_message": self.error_message
        }
    
    def _set_state(self, new_state: AgentState, error_message: Optional[str] = None):
        """
        设置agent状态
        
        Args:
            new_state: 新状态
            error_message: 错误消息（如果有）
        """
        old_state = self.state
        self.state = new_state
        self.error_message = error_message
        
        if new_state == AgentState.ERROR and error_message:
            self.logger.error(f"Agent {self.name} entered ERROR state: {error_message}")
        else:
            self.logger.info(f"Agent {self.name} state changed: {old_state.value} -> {new_state.value}")
    
    def _ensure_state(self, required_states: list[AgentState], operation: str):
        """
        确保agent处于指定状态之一
        
        Args:
            required_states: 要求的状态列表
            operation: 操作名称
            
        Raises:
            AgentStateError: 如果状态不正确
        """
        if self.state not in required_states:
            raise AgentStateError(
                f"Agent {self.name} cannot {operation} in state {self.state.value}. "
                f"Required states: {[s.value for s in required_states]}"
            )
    
    async def safe_initialize(self) -> bool:
        """
        安全初始化wrapper，包含错误处理
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self._set_state(AgentState.PROCESSING)
            self.initialization_time = asyncio.get_event_loop().time()
            
            success = await self.initialize()
            
            if success:
                self._set_state(AgentState.IDLE)
                self.logger.info(f"Agent {self.name} initialized successfully")
            else:
                self._set_state(AgentState.ERROR, "Initialization failed")
                
            return success
            
        except Exception as e:
            self._set_state(AgentState.ERROR, str(e))
            self.logger.error(f"Failed to initialize agent {self.name}: {e}")
            return False
    
    async def safe_start(self) -> bool:
        """
        安全启动wrapper，包含错误处理
        
        Returns:
            bool: 启动是否成功
        """
        try:
            self._ensure_state([AgentState.IDLE], "start")
            
            success = await self.start()
            
            if success:
                self.is_running = True
                self._set_state(AgentState.LISTENING)
                self.logger.info(f"Agent {self.name} started successfully")
            else:
                self._set_state(AgentState.ERROR, "Start failed")
                
            return success
            
        except Exception as e:
            self._set_state(AgentState.ERROR, str(e))
            self.logger.error(f"Failed to start agent {self.name}: {e}")
            return False
    
    async def safe_shutdown(self) -> None:
        """
        安全关闭wrapper，包含错误处理
        """
        try:
            self._set_state(AgentState.PROCESSING)
            self.is_running = False
            
            await self.shutdown()
            
            self._set_state(AgentState.IDLE)
            self.logger.info(f"Agent {self.name} shutdown successfully")
            
        except Exception as e:
            self._set_state(AgentState.ERROR, str(e))
            self.logger.error(f"Failed to shutdown agent {self.name}: {e}")
    
    # 上下文管理器支持
    async def __aenter__(self):
        """异步上下文管理器进入"""
        await self.safe_initialize()
        await self.safe_start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.safe_shutdown()
        return False  # 不抑制异常 