#!/bin/bash

# Audio Agent Web Client Startup Script
# 音频代理Web客户端启动脚本

echo "🚀 Starting Audio Agent Web Client..."
echo "正在启动音频代理Web客户端..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found. Please install Python3."
    echo "❌ 未找到Python3，请安装Python3。"
    exit 1
fi

# Navigate to client directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 Current directory: $SCRIPT_DIR"
echo "📁 当前目录: $SCRIPT_DIR"

# Check if index.html exists
if [ ! -f "index.html" ]; then
    echo "❌ index.html not found. Make sure you're in the client directory."
    echo "❌ 未找到index.html文件，请确保您在client目录中。"
    exit 1
fi

# Start HTTP server
PORT=8080
echo "🌐 Starting HTTP server on port $PORT..."
echo "🌐 在端口 $PORT 启动HTTP服务器..."
echo ""
echo "📖 Instructions | 使用说明:"
echo "1. Open your browser and go to: http://localhost:$PORT"
echo "   在浏览器中打开: http://localhost:$PORT"
echo "2. Make sure Audio Agent server is running (python start.py)"
echo "   确保音频代理服务器正在运行 (python start.py)"
echo "3. Click 'Connect' to start the session"
echo "   点击'Connect'开始会话"
echo ""
echo "Press Ctrl+C to stop the server"
echo "按 Ctrl+C 停止服务器"
echo "================================"

# Try different Python commands
if command -v python3 &> /dev/null; then
    python3 -m http.server $PORT
elif command -v python &> /dev/null; then
    python -m http.server $PORT
else
    echo "❌ No suitable Python found."
    echo "❌ 未找到合适的Python版本。"
    exit 1
fi 