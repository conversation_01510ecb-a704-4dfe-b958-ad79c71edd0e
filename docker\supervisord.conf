[supervisord]
nodaemon=true
user=appuser
logfile=/app/logs/supervisord.log
pidfile=/tmp/supervisord.pid
loglevel=info

[program:audio-agent]
command=python -m uvicorn app:app --host 0.0.0.0 --port 8000 --log-level info --workers 1
directory=/app
autostart=true
autorestart=true
user=appuser
stdout_logfile=/app/logs/audio_agent.log
stderr_logfile=/app/logs/audio_agent_error.log
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1"
priority=100
startsecs=10
startretries=5
stopwaitsecs=30

[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700
chown=appuser:appuser

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock 