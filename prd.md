### **产品需求文档 (PRD): 主动式实时 AI 对话助手**

| **文档版本** | 2.1 |
| :--- | :--- |
| **文档状态** | 修订稿 |
| **创建日期** | 2025 年 7 月 22 日 |
| **作者** | AI Assistant |
| **Default Language** | English |

-----

### **1. 项目概述与愿景**

#### **1.1. 项目简介**

本项目旨在开发一款先进的、基于统一后端服务的实时 AI 语音对话助手。其核心目标是构建一个支持中英双语、具备极低延迟交互能力、架构高度集成的对话系统。

#### **1.2. 核心愿景**

本项目致力于实现一个范式转变：从当前市场上普遍存在的、用户可以打断 AI（“barge-in”）的被动式助手，演进为一个能够**主动且智能地打断人类用户**、从而主动引导、重定向甚至掌控对话流的**主动式对话伙伴**。

#### **1.3. 关键挑战与目标**

最具挑战性的特性是赋予 AI 主动打断的能力。 这要求系统不仅能“听懂”，更能“边听边想”，在用户话音未落之时，就已完成对内容的实时分析、决策并准备好发起打断。 此版本的架构将通过在单一 FastAPI 应用内实现高效的事件路由和会话管理，大幅简化系统复杂性并降低运营成本，使 AI 从被动的服务提供者转变为主动的对话管理者。

-----

### **2. 核心设计原则与理论基础**

#### **2.1. Dispider 原则：解耦感知、决策与反应**

本项目的架构设计严格遵循“Dispider”原则，将实时交互系统的三大核心环节解耦，使其能够通过异步并发任务完美解决“AI 如何边听边想”的难题：

  - **感知 (Perception):** 由专门的逻辑处理器处理来自单一实时 API 的音频流与语音转写事件。
  - **决策 (Decision):** 由多个独立的后台任务对感知到的信息流进行持续分析与并发决策。
  - **反应 (Reaction):** 由专门的逻辑处理器负责生成和播放 AI 的语音回复，其行为完全受决策层控制。

#### **2.2. AI 主动打断的理论框架**

AI 的打断行为必须基于对对话状态和用户意图的深刻理解。本系统将打断行为分为两大类。

  - **合作型打断 (Cooperative Interruption):** 由**Tactical Thinker**处理，追求快速响应。

      - **自然附和 (Natural Agreement):** "xxxxx" → AI: "确实是这样的"。
      - **情感支持 (Emotional Support):** 用户表达困惑时主动协助 → AI: "没事的/问题不大啦"。
      - **更新:** 暂时放弃实时通过音频打断，而是只在文字层面输出emoji或者简单带方框的语言或者两者结合比如【确实是👍】，不再尝试直接生成附和型声音。

  - **干扰型/话题控制型打断 (Disruptive/Topic Control Interruption):** 由**Strategic Thinker**处理，允许更深度的思考。

      - **核心应用场景:** 在用户偏离主题、重复冗长或发言超时，AI 必须进行干预以控制对话方向。
          - **话题控制:** 面试中偏题 → AI: "让我们回到技术问题上" (只在特定模式比如面试官模式生效)。
          - **重复冗长管理:** 回答过于冗长 → AI: "请简洁一些，重点是什么？"。
      - **技术实现:** 持续运行，边听边想，主动推送决策。 储存约20轮对话的记忆（包括用户和AI的转录）作为上下文，以实时判断重复冗长等情况。

  - **打断仲裁 (Interruption Arbitration):**

      - **并行处理:** Strategic Thinker (干扰型) 和 Tactical Thinker (合作型) 并行工作，因为它们处理的事件类型不同。
      - **事件驱动:** Decider 采用纯事件驱动决策，每个 Thinker 独立工作并主动推送决策，无超时等待。

AI 将基于此分类，结合上下文，执行如**保持话语权 (Hold Floor)**、\*\*让出话语权 (Yield Floor)\*\*等具体的对话策略。

-----

### **3. 系统架构：集成式多任务系统 (Integrated Multi-Task System)**

为实现上述目标，我们设计一个基于 Python FastAPI 的单体（Monolithic）但模块化（Modular）的后端服务。 该架构通过在单个 WebSocket 会话中进行内部事件路由，取代了外部消息总线，从而简化了部署和维护。

#### **3.1. 架构图**

```
+------------------+      +-------------------------------------------------+
|   客户端 (Web)   |<----->|          FastAPI 后端 (Cloud Run)               |
| (Single WebSocket|      |      (统一会话管理, 异步事件路由)             |
|   Connection)    |      +---------------------+-------------------------+
+------------------+                            | (双向流)
                                                v
                  +-------------------------------------------------------+
                  |         实时语音API (阿里云通义千问 qwen-omni-turbo-realtime)  |
                  +-------------------------------------------------------+
                      ^                    ^                     |
                      | (音频输入)         | (打断/回复指令)      | (转写结果)
                      |                    |                     |
+---------------------+--------------------+---------------------+----------------------+
|                     Orchestrator (在FastAPI应用内)                                      |
|                                                                                      |
| +------------+  (转写Delta)  +-----------------+  (合作打断建议)   +-----------+ (最终决策) |
| | Listener   |-------------->| Tactical Thinker|------------------->|           |---------->|
| | (事件处理器) |               | (doubao-lite)   |                   | Decider   |           |
| +------------+               +-----------------+                   | (doubao-lite) |           |
|       |                      +-----------------+  (干扰打断建议)   |           |           |
|       +--------------------->| Strategic Thinker|------------------->|           |           |
|                              | (doubao-lite)   |                   +-----------+           |
|                              | (长期记忆管理)    |                                           |
|                              +-----------------+                                  (指令) |
|                                                                                      |
|                                    +-----------+ <-----------------------------------+
|                                    | Speaker   |
|                                    | (事件处理器) |
|                                    +-----------+                                       |
+--------------------------------------------------------------------------------------+
```

#### **3.2. 智能体/模块详细职责**

  - **Orchestrator (协调者):**

      - **角色:** 系统的中枢神经与会话状态管理器，是 FastAPI 应用的核心逻辑。
      - **职责:**
          - 为每个客户端连接创建一个统一的实时会话。
          - 管理整个对话的宏观状态（如 `AI_SPEAKING`, `USER_SPEAKING`）。
          - 作为所有内部模块之间事件分发和任务协调的路由器。
          - 接收**Decider**的最终决策，并向实时语音 API 下达打断或回复指令。

  - **Listener (倾听者):**

      - **角色:** 系统的“耳朵”，一个逻辑事件处理器。
      - **职责:**
          - 处理从实时语音 API（阿里云通义千问）接收到的`transcript_delta`（单个词或短语）事件。
          - 将收到的转写 Delta 作为内部事件，异步地分发给**Tactical Thinker**和**Strategic Thinker**。

  - **Thinker (思考者) - 双并发进程模型:**

      - **Tactical Thinker (战术思考者):**
          - **角色:** 快速、低层语义分析器，专注于合作型打断。
          - **实现:** `字节跳动豆包 doubao-1-5-lite-32k-250115`，因其优化的响应速度和对战术任务的高效处理能力而被选中。
          - **职责:** 接收`transcript_delta`，进行快速意图判断（如：“自然附和”、“情感支持”），并将“合作型打断”建议（附带打断文本/emoji）发送给**Decider**。
      - **Strategic Thinker (战略思考者):**
          - **角色:** 上下文与策略分析器，专注于干扰型打断。
          - **实现:** `字节跳动豆包 doubao-1-5-lite-32k-250115`，用于管理对话历史和上下文。
          - **职责:** 维护完整的对话历史（长期记忆），分析用户对话是否偏离核心议题。 在判断需要干预时，向**Decider**发送“干扰型打断”建议。

  - **Decider (决策者):**

      - **角色:** 系统的事件分发核心。
      - **实现:** `字节跳动豆包 doubao-1-5-lite-32k-250115`，确保决策过程的一致性。
      - **职责:**
          - 并发监听来自两个 Thinker 的打断建议。
          - 基于事件类型和特定角色（如“面试官”）的规则集，进行分发决策。
          - 立即将最终决策（如 `decision: interrupt`, `text: '谢谢，我们回到Python项目的话题上...'`）传递给**Orchestrator**。

  - **Speaker (发言者):**

      - **角色:** 系统的“嘴巴”，一个逻辑事件处理器。
      - **职责:**
          - 接收来自**Orchestrator**的指令，无论是生成常规回复还是执行打断。
          - 将文本内容通过实时语音 API（阿里云通义千问）的流式文本到语音（TTS）能力转换为音频流，并通过同一 WebSocket 连接传回客户端播放。

-----

### **4. 技术实现与数据流**

#### **4.1. 推荐技术栈**

  - **客户端-后端通信:** WebSocket。
  - **后端服务:** 基于 Cloud Run 的容器化 FastAPI 应用，利用其原生的异步能力和 WebSocket 支持。
  - **统一实时对话引擎:** 阿里云通义千问`qwen-omni-turbo-realtime`，因其支持流式语音输入、文本输出和音频输出的端到端能力。
  - **统一思考与决策引擎:** `字节跳动豆包 doubao-1-5-lite-32k-250115`，用于快速分析、上下文记忆和决策。

#### **4.2. 核心工作流：AI 主动打断**

1.  **感知与分发 (Perception & Distribution):**

      - 客户端通过单一 WebSocket 将 PCM 音频块持续发送到后端的 FastAPI 应用。
      - **Orchestrator**将音频实时转发给`qwen-omni-turbo-realtime` API。
      - API 返回`audio_transcript.delta`事件。
      - **Listener**模块捕获此事件，并立即创建两个并行的异步任务，将转写内容分别发送给**Tactical Thinker**和**Strategic Thinker**。

2.  **并发决策 (Concurrent Decision):**

      - **Tactical Thinker** (`doubao-1-5-lite-32k-250115`)快速分析转写内容，判断是否存在合作型打断机会。 如果存在，立即向**Decider**发送建议。
      - 同时，**Strategic Thinker** (`doubao-1-5-lite-32k-250115`)结合完整的对话历史分析转写内容，判断用户是否偏离主题。 如果满足条件，则向**Decider**发送干扰型打断建议。
      - **Decider** (`doubao-1-5-lite-32k-250115`)接收来自任一思考者的建议并立即进行处理和分发，因为两个Thinker处理不同的事件类型，无需等待或仲裁。
      - **Decider**将最终的指令（包含打断文本和类型）发送给**Orchestrator**。

3.  **反应与执行 (Reaction & Execution):**

      - **Orchestrator**收到**Decider**的最终指令后，立即执行打断。
      - 它向`qwen-omni-turbo-realtime` API 发送一个指令，要求**立即停止处理用户当前的语音输入，并清空所有待处理的音频缓冲区**。 这是实现 AI 主动打断的关键技术。
      - 紧接着，**Orchestrator**通过**Speaker**模块，将**Decider**提供的打断文本发送给同一 API 进行 TTS 合成。
      - 合成的音频流通过同一 WebSocket 连接实时传回客户端进行播放，果断地打断用户，完成主动对话控制。

#### **4.3. 核心工作流：AI 音频实时输出 (实现总结)**

本部分基于已验证的成功实现，对 AI 音频输出的技术方案进行补充说明。

  - **双通道音频传输架构:** 为确保音频输出的低延迟和高可靠性，系统在逻辑上采用智能降级的双通道架构。

      - **主通道 (Direct WebSocket):** AI 合成的 PCM16 音频数据（Base64 编码）优先通过当前会话的 WebSocket 连接直接推送至客户端，以实现最低延迟。
      - **备用通道 (Internal Message Bus Fallback):** 为应对直接推送失败或连接不稳定的情况，系统内部实现了一个轻量级的消息总线作为备用。 音频数据会同时发布到该总线，作为一种自动降级的高可靠性保障。

  - **前端智能音频播放器:** 客户端实现了一个`DirectAudioPlayer`，具备以下关键能力，以确保流畅、无误的播放体验：

      - **音频队列化:** 所有接收到的音频块（chunk）都进入一个先进先出的队列，确保按序播放，避免音频错乱。
      - **数据去重:** 每个音频块都附带唯一 ID，播放器会记录并检查已处理的 ID，有效防止因双通道冗余传输可能导致的音频重复播放问题。
      - **实时解码与播放:** 利用现代浏览器的 Web Audio API 实时解码接收到的 Base64 PCM 数据并进行播放。

  - **事件驱动的音频处理:** 后端**Orchestrator**在收到 AI 生成的完整音频响应后，会同时尝试通过主通道（Direct WebSocket）和备用通道（Internal Message Bus）分发音频。 此设计在实践中已证明可将端到端音频延迟稳定在**200-300ms**，远超设计目标，并保证了传输的成功率。

-----

### **5. 核心功能需求**

| ID | 功能模块 | 需求描述 | 优先级 |
| :--- | :--- | :--- | :--- |
| F-01 | **统一双向流** | 系统必须通过单一 WebSocket 连接处理双向的音频输入、转写事件和音频输出。 | **P0** |
| F-02 | **实时转写 Delta** | **Listener**必须能够处理流式的中间转写结果，以实现“边听边想”。 | **P0** |
| F-03 | **并发思考与主动打断** | 系统必须能并发处理不同层级的思考任务，并根据明确的规则发起主动打断。 | **P0** |
| F-04 | **可配置角色** | **Decider**和**Thinker**的行为逻辑必须通过提示工程高度可配置，以适应不同场景（面试官、游戏裁判、车载助手）。 | **P1** |
| F-05 | **集成会话管理** | **Orchestrator**必须在 FastAPI 应用内可靠地管理会话状态，取代外部依赖。 | **P1** |
| F-06 | **双语支持** | 系统必须同等支持流畅的中文和英文对话。 | **P1** |

-----

### **6. 非功能性需求**

| ID | 类别 | 需求描述 |
| :--- | :--- | :--- |
| NF-01 | **延迟** | 从用户话音发出到 AI 打断音频开始播放的端到端延迟（P-D-R）应控制在 **500ms** 以内。 |
| NF-02 | **可扩展性** | 架构必须是云原生的，能够在 Cloud Run 上通过容器化服务轻松扩展，以应对高并发用户。 |
| NF-03 | **可靠性** | 系统各组件应具备高可用性，并在 FastAPI 内有适当的错误处理与重试机制。 |
| NF-04 | **成本效益** | 架构简化，并通过统一 AI 模型调用（使用字节跳动豆包等高性价比国产模型）优化整体运营成本。 |

-----

### **7. 技术实现架构**

#### **7.1. AI 模型选型原则**

本系统采用"**统一 AI 架构**"，针对不同处理需求通过不同的提示工程来调用统一的模型，兼顾性能、成本和网络稳定性：

| 处理层级 | 选用模型 | 核心特点 | 应用场景 |
| :--- | :--- | :--- | :--- |
| **语音处理层** | 阿里千问 qwen-omni-turbo-realtime | 专为中文优化的实时语音模型 | 语音转写、语音合成 |
| **快速分析层** | 豆包 doubao-1-5-lite-32k-250115 | 轻量级、低延迟、高并发 | 实时意图识别、合作型打断决策 |
| **上下文分析层** | 豆包 doubao-1-5-lite-32k-250115 | 上下文记忆与分析 | 支持长对话历史分析，统一模型架构 |

#### **7.2. 网络环境优化**

**针对中国大陆网络环境的技术选择**：

  - **国产 AI 模型优先**：豆包和千问均为国内厂商，网络延迟显著低于海外 API。
  - **统一 API 接口**：所有 AI 调用使用 OpenAI 兼容格式，降低开发和维护复杂度。
  - **高可用架构**：避免因国际网络波动导致的服务中断。
  - **成本控制**：国产模型定价更具竞争力，单次调用成本更低。

#### **7.3. 实时性能保障**

  - **并发处理**：Tactical Thinker 和 Strategic Thinker 并行工作，无阻塞等待。
  - **事件驱动决策**：基于事件进行快速决策，确保实时响应。
  - **缓存策略**：智能缓存常见分析结果，减少重复 API 调用。
  - **超时控制**：每层 AI 调用设置合理超时，避免长时间等待。

-----

### **8. 多场景适配性**

本架构设计具备高度灵活性，可通过调整 **Decider**智能体的规则和 **Thinker**智能体的提示来适应不同应用场景：

  - **AI 面试官:**

      - **Decider 规则:** 严格、以任务为导向。 对偏离主题、回答冗长、提供不相关信息等行为设置低容忍度阈值。
      - **打断风格:** 礼貌但坚定，例如：“感谢你的分享，让我们回到刚才关于你项目经历的问题上。”

  - **狼人杀 AI 法官/玩家:**

      - **Decider 规则:** 响应速度优先。 快速捕捉逻辑漏洞、发言超时、互相攻击等游戏行为。
      - **打断风格:** 简短、直接、甚至具有煽动性，例如：“时间到！请停止发言！”或“你的发言和上一轮有矛盾！”

  - **车载语音助理:**

      - **Decider 规则:** 极其保守。 主动打断仅限于最高优先级的事件，如**紧急安全警报**或**关键导航指令**（例如“立即在下一个路口右转！”）。 绝大多数情况下，遵循用户讲完再回应的原则。
      - **打断风格:** 清晰、简洁、以安全为第一要务。