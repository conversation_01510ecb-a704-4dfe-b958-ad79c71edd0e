# FastAPI和WebSocket支持
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# AI模型SDK - OpenAI兼容库(用于豆包和千问)
openai
httpx[socks]

# 数值计算（保留用于其他组件）
numpy==1.24.3
scipy==1.11.4

# 环境配置
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 异步支持
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# 日志和监控
loguru==0.7.2
structlog==23.2.0

# 工具库
aiohttp==3.9.1
pyyaml==6.0.1
thefuzz>=0.19.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0 