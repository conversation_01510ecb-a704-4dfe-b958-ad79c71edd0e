"""
Multi-Agent System for Proactive Real-time AI Conversation Assistant

This module implements the Dispider architecture with the following agents:
- Orchestra<PERSON>: Central coordinator and state manager
- Listener: Audio perception and STT processing  
- Tactical Thinker: Fast semantic analysis (SLM)
- Strategic Thinker: Deep content generation (LLM)
- Decider: Interruption decision logic
- Speaker: TTS audio output
"""

from .orchestrator import Orchestrator
from .listener import Listener
from .tactical_thinker import TacticalThinker
from .strategic_thinker import StrategicThinker
from .decider import Decider
from .speaker import Speaker

__all__ = [
    "Orchestrator",
    "Listener", 
    "TacticalThinker",
    "StrategicThinker",
    "Decider",
    "Speaker"
] 