"""
Speaker Agent - 负责AI语音输出和合成

使用阿里千问qwen-omni-turbo-realtime API进行实时语音合成，
保持与原有接口的完全兼容性。
"""

import asyncio
import json
import base64
from datetime import datetime
from loguru import logger
from typing import Optional, Dict, Any, Callable

from core.base_agent import BaseAgent
from core.realtime_manager import RealtimeManager
from core.client_factory import ClientFactory
from core.config import get_realtime_config, get_qwen_voice_mapping
from core.message_bus import message_bus, TopicNames, create_message
from core.event_types import EventType, MessageType

class SpeakerAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="Speaker")
        self.agent_id = "speaker"  # 添加缺失的agent_id属性
        self.realtime_manager: Optional[RealtimeManager] = None
        self.is_speaking = False
        self.audio_callback: Optional[Callable] = None
        self.text_callback: Optional[Callable] = None
        self.current_voice = "Ethan"  # 默认音色
        
        # 向后兼容性：保持原有的音频缓冲区接口
        self.audio_buffer = []
        self.buffer_size = 4096
        
    async def initialize(self):
        """初始化Speaker，使用全局RealtimeManager实例"""
        try:
            # 使用全局RealtimeManager实例而不是创建独立连接
            from core.realtime_manager import get_global_manager
            self.realtime_manager = get_global_manager()
            
            # 确保全局实例存在
            if not self.realtime_manager:
                raise Exception("Global RealtimeManager instance not found. Please initialize it first.")
            
            # 注册事件处理器（保持向后兼容）
            self.realtime_manager.register_event_handler(
                "response.audio.delta",
                self._handle_audio_delta
            )
            
            self.realtime_manager.register_event_handler(
                "response.audio.done",
                self._handle_audio_done
            )
            
            self.realtime_manager.register_event_handler(
                "response.text.delta", 
                self._handle_text_delta
            )
            
            self.realtime_manager.register_event_handler(
                "response.text.done",
                self._handle_text_done
            )
            
            # 全局RealtimeManager应该已经连接，只需验证连接状态
            if not self.realtime_manager.is_connected:
                raise Exception("Global RealtimeManager is not connected to 阿里千问API")
            
            # 设置消息总线订阅
            await self._setup_message_subscriptions()
            
            logger.info("SpeakerAgent initialized with global RealtimeManager instance")
            return True
                
        except Exception as e:
            logger.error(f"Failed to initialize SpeakerAgent: {e}")
            raise
    
    async def start(self) -> bool:
        """
        启动SpeakerAgent代理
        
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info("Starting SpeakerAgent...")
            
            # 确保已经初始化
            if not self.realtime_manager or not self.realtime_manager.is_connected:
                await self.initialize()
            
            # 开始语音输出（如果之前未开始）
            if not self.is_speaking:
                await self.start_speaking()
            
            # 设置运行状态标志
            self.is_running = True
            
            logger.info("SpeakerAgent started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start SpeakerAgent: {e}")
            self.is_running = False
            return False
    
    async def shutdown(self) -> None:
        """
        关闭SpeakerAgent代理，清理资源
        """
        try:
            logger.info("Shutting down SpeakerAgent...")
            
            # 停止语音输出
            await self.stop_speaking()
            
            # 断开连接和清理资源
            await self.cleanup()
            
            logger.info("SpeakerAgent shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during SpeakerAgent shutdown: {e}")
    
    async def start_speaking(self, audio_callback: Optional[Callable] = None, 
                           text_callback: Optional[Callable] = None):
        """
        开始语音输出（保持向后兼容接口）
        
        Args:
            audio_callback: 音频数据回调函数
            text_callback: 文本数据回调函数
        """
        if not self.realtime_manager or not self.realtime_manager.is_connected:
            await self.initialize()
            
        self.audio_callback = audio_callback
        self.text_callback = text_callback
        self.is_speaking = True
        
        logger.info("Started speech output")
        
    async def stop_speaking(self):
        """停止语音输出"""
        self.is_speaking = False
        self.audio_callback = None
        self.text_callback = None
        
        logger.info("Stopped speech output")
    
    def clear_audio_output_callback(self):
        """
        清理音频输出回调函数
        
        用于WebSocket连接断开时清理回调，防止向已关闭的连接发送数据
        """
        try:
            logger.debug("清理Speaker音频输出回调")
            
            # 清理音频和文本回调
            self.audio_callback = None
            self.text_callback = None
            
            logger.debug("✅ Speaker音频输出回调已清理")
            
        except Exception as e:
            logger.error(f"清理Speaker回调时出错: {e}")
    
    async def start_session(self, session_id: str) -> bool:
        """
        启动会话级别的语音输出
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info(f"启动Speaker会话: {session_id}")
            
            # 确保已经初始化和启动
            if not self.realtime_manager or not self.realtime_manager.is_connected:
                await self.initialize()
            
            if not self.is_speaking:
                await self.start_speaking()
            
            # 会话级别的配置可以在这里添加
            # 例如：会话特定的音色设置、输出参数等
            
            logger.info(f"✅ Speaker会话启动成功: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动Speaker会话失败 {session_id}: {e}")
            return False
    
    async def set_voice(self, voice_name: str) -> bool:
        """
        设置音色（保持向后兼容接口，支持音色映射）
        
        Args:
            voice_name: 音色名称（支持原系统音色自动映射到千问音色）
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 使用音色映射
            if self.realtime_manager:
                mapped_voice = self.realtime_manager.map_voice_to_qwen(voice_name)
                success = await self.realtime_manager.update_session_config({
                    "voice": mapped_voice
                })
                
                if success:
                    self.current_voice = mapped_voice
                    logger.info(f"Voice set to: {mapped_voice} (mapped from {voice_name})")
                    return True
                else:
                    logger.error(f"Failed to set voice: {voice_name}")
                    return False
            else:
                # 离线设置，等待连接后应用
                voice_mapping = get_qwen_voice_mapping()
                self.current_voice = voice_mapping.get(voice_name, "Ethan")
                logger.info(f"Voice queued: {self.current_voice} (mapped from {voice_name})")
                return True
                
        except Exception as e:
            logger.error(f"Error setting voice: {e}")
            return False
    
    async def speak_text(self, text: str) -> bool:
        """
        [DEPRECATED] This method is now deprecated in favor of speak_with_context.
        It uses a simple instruction that overwrites the session's persona.
        """
        logger.warning("speak_text is deprecated. Use speak_with_context for contextual responses.")
        # Fallback to new method with generic reason
        return await self.speak_with_context(text, "Deprecated text instruction")

    async def speak_with_context(self, text_to_speak: str, reason: str) -> bool:
        """
        Makes the AI speak with full context by updating session instructions.
        This is the new standard method for AI speech.
        
        Args:
            text_to_speak: The sentence the AI should say.
            reason: The context or reason for speaking (e.g., from StrategicThinker).
            
        Returns:
            bool: If the request was successfully sent.
        """
        if not self.realtime_manager or not self.realtime_manager.is_connected:
            logger.error("RealtimeManager not available for contextual speech.")
            return False
            
        try:
            # 1. 获取当前的AI persona (e.g., "You are an interviewer...")
            current_persona = self.realtime_manager.get_current_instructions() or "You are a helpful assistant."
            
            # 2. 构建新的、包含打断上下文的指令
            new_instructions = (
                f"{current_persona}\n\n"
                f"--- IMMEDIATE CONTEXT ---\n"
                f"[System Note: You have just decided to interrupt the user. "
                f"The reason is: '{reason}'. "
                f"Now, seamlessly say the following sentence to the user]:\n"
                f"{text_to_speak}"
            )
            
            # 3. 更新会话配置，将上下文注入
            config_updated = await self.realtime_manager.update_session_config({
                "instructions": new_instructions
            })
            
            if not config_updated:
                logger.error("Failed to update session config with interruption context.")
                return False

            # 4. 请求AI根据新的上下文生成回应 (这会触发语音合成)
            # 这是让AI说话的正确方式，而不是鹦鹉学舌
            # --- START OF FIX ---
            # 修复：发送一个空的文本输入来可靠地触发AI响应
            # 而不是使用 create_response()
            # 🎯 新增：重试机制提高可靠性
            response_triggered = False
            max_retries = 3
            retry_delay = 0.1  # 100ms延迟
            
            for attempt in range(max_retries):
                try:
                    response_triggered = await self.realtime_manager.send_text_input(text="")
                    if response_triggered:
                        logger.info(f"Contextual speech triggered successfully on attempt {attempt + 1}")
                        break
                    else:
                        logger.warning(f"Failed to trigger contextual response on attempt {attempt + 1}/{max_retries}")
                        if attempt < max_retries - 1:  # 不是最后一次尝试
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 1.5  # 指数退避
                except Exception as e:
                    logger.error(f"Exception during send_text_input attempt {attempt + 1}: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 1.5
            # --- END OF FIX ---

            if response_triggered:
                logger.info(f"Contextual speech requested for: '{text_to_speak}' (reason: {reason})")
                return True
            else:
                logger.error(f"Failed to trigger contextual response after {max_retries} attempts.")
                return False

        except Exception as e:
            logger.exception(f"Error in speak_with_context: {e}")
            return False
    
    async def _handle_audio_delta(self, event_type: str, data: Dict[str, Any]):
        """处理音频增量事件"""
        try:
            audio_b64 = data.get("delta", "")
            
            if audio_b64:
                # 解码音频数据
                audio_data = base64.b64decode(audio_b64)
                
                # 向后兼容：添加到音频缓冲区
                self.audio_buffer.append(audio_data)
                if len(self.audio_buffer) > self.buffer_size:
                    self.audio_buffer.pop(0)
                
                # 触发向后兼容的回调
                if self.audio_callback:
                    await self.audio_callback(audio_data)
                    
                logger.debug(f"Audio delta received: {len(audio_data)} bytes")
                
        except Exception as e:
            logger.error(f"Error handling audio delta: {e}")
    
    async def _handle_audio_done(self, event_type: str, data: Dict[str, Any]):
        """处理音频完成事件"""
        try:
            logger.info("Audio synthesis completed")
            
            # 向消息总线发送事件（保持向后兼容）
            await message_bus.publish("audio.completed", {
                "agent": self.name,
                "timestamp": data.get("timestamp"),
                "total_chunks": len(self.audio_buffer)
            })
            
        except Exception as e:
            logger.error(f"Error handling audio completion: {e}")
    
    async def _handle_text_delta(self, event_type: str, data: Dict[str, Any]):
        """处理文本增量事件"""
        try:
            text_delta = data.get("delta", "")
            
            if text_delta:
                # 触发向后兼容的回调
                if self.text_callback:
                    await self.text_callback({
                        "text": text_delta,
                        "is_partial": True,
                        "timestamp": data.get("timestamp")
                    })
                    
                logger.debug(f"Text delta received: '{text_delta}'")
                
        except Exception as e:
            logger.error(f"Error handling text delta: {e}")
    
    async def _handle_text_done(self, event_type: str, data: Dict[str, Any]):
        """处理文本完成事件"""
        try:
            complete_text = data.get("text", "")
            
            logger.info(f"Text synthesis completed: '{complete_text}'")
            
            # 向消息总线发送事件（保持向后兼容）
            await self.message_bus.publish("text.completed", {
                "agent": self.name,
                "text": complete_text,
                "timestamp": data.get("timestamp")
            })
            
        except Exception as e:
            logger.error(f"Error handling text completion: {e}")
    
    async def interrupt_speech(self) -> bool:
        """
        中断当前语音输出（通过千问API的response.cancel机制）
        
        Returns:
            bool: 中断是否成功
        """
        if not self.realtime_manager or not self.realtime_manager.is_connected:
            logger.error("RealtimeManager not available or not connected")
            return False
            
        try:
            # 使用千问API的response.cancel机制中断当前响应
            cancel_event = {
                "type": "response.cancel"
            }
            
            success = await self.realtime_manager.send_event(cancel_event)
            if success:
                logger.info("Speech interrupted successfully via response.cancel")
            else:
                logger.error("Failed to send response.cancel")
            
            # --- Start of Fix ---
            # 向前端发送停止播放指令
            from core.message_bus import message_bus, TopicNames, create_message
            from core.event_types import EventType, MessageType
            
            await message_bus.publish(
                TopicNames.AUDIO_PLAYBACK,
                create_message(
                    source_agent=self.agent_id,
                    event_type=EventType.AUDIO_PLAYBACK_STOP,
                    message_type=MessageType.SPEAKER_COMMAND,
                    data={
                        "command": "stop",
                        "reason": "speech_interrupted",
                        "timestamp": datetime.now().isoformat()
                    }
                )
            )
            logger.info("Sent audio playback stop command to frontend")
            # --- End of Fix ---
            
            return success
        except Exception as e:
            logger.error(f"Error interrupting speech: {e}")
            return False
    
    async def get_current_voice(self) -> str:
        """
        获取当前音色（保持向后兼容接口）
        
        Returns:
            str: 当前音色名称
        """
        return self.current_voice
    
    async def get_supported_voices(self) -> list:
        """
        获取支持的音色列表（保持向后兼容接口）
        
        Returns:
            list: 支持的音色列表
        """
        voice_mapping = get_qwen_voice_mapping()
        return list(voice_mapping.keys())
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop_speaking()
            
            if self.realtime_manager:
                await self.realtime_manager.disconnect()
                self.realtime_manager = None
                
            self.audio_buffer.clear()
            logger.info("SpeakerAgent cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during SpeakerAgent cleanup: {e}")
    
    async def _setup_message_subscriptions(self):
        """设置消息总线订阅"""
        try:
            # 连接到消息总线
            if not await message_bus.connect():
                logger.error("Failed to connect to message bus")
                return False
                
            # 订阅TTS命令主题
            await message_bus.subscribe(
                TopicNames.TTS_COMMANDS,
                self.handle_tts_command
            )
            
            # 订阅打断命令主题
            await message_bus.subscribe(
                TopicNames.INTERRUPTION_COMMAND,
                self.handle_interruption_command
            )
            
            logger.info("Speaker message bus subscriptions set up successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup message subscriptions: {e}")
            return False
    
    async def handle_tts_command(self, topic: str, message_data: Dict[str, Any]):
        """处理TTS命令"""
        try:
            data = message_data.get("data", {})
            command = data.get("command")
            text = data.get("text")
            session_id = data.get("session_id")
            task_id = data.get("task_id")
            
            logger.info(f"Received TTS command: {command} for session {session_id}")
            
            if command == "start_tts" and text:
                # 使用新的上下文感知方法
                success = await self.speak_with_context(text, "Standard TTS request")
                
                if success:
                    logger.info(f"TTS started successfully for task {task_id}")
                else:
                    logger.error(f"Failed to start TTS for task {task_id}")
            
            # 🎯 新增：支持新的上下文感知语音合成
            elif command == "speak_with_context" and text:
                reason = data.get("reason", "No reason provided.")
                success = await self.speak_with_context(text, reason)
                if success:
                    logger.info("Contextual TTS started successfully.")
                else:
                    logger.error("Failed to start contextual TTS.")
            
            elif command == "stop_tts":
                # 停止当前语音输出
                success = await self.interrupt_speech()
                logger.info(f"TTS stopped for task {task_id}: {success}")
                
        except Exception as e:
            logger.error(f"Error handling TTS command: {e}")
    
    async def handle_interruption_command(self, topic: str, message_data: Dict[str, Any]):
        """处理打断命令 - 简化版：只负责停止，不负责说话"""
        try:
            data = message_data.get("data", {})
            interruption_type = data.get("interruption_type")
            session_id = data.get("session_id")
            reason = data.get("reason", "Unknown")
            
            logger.info(f"Received interruption command: {interruption_type} - {reason}")
            
            # 🎯 核心改变: Speaker的职责仅仅是"停止"，而不是"停止并立即开始"。
            # "开始"的任务将由Orchestrator在确认停止后，通过新的事件流触发。
            await self.interrupt_speech()
            logger.info(f"Speech interruption completed for session {session_id}")
            
        except Exception as e:
            logger.error(f"Error handling interruption command: {e}") 

# 向后兼容别名 - 保持与现有代码的兼容性  
Speaker = SpeakerAgent 