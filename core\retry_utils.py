"""
重试工具for Audio Agent系统
提供指数退避重试策略
"""

import asyncio
import logging
from functools import wraps
from typing import Callable, Any, Optional, Tuple, Type
from .exceptions import APIClientError, WebSocketConnectionError

logger = logging.getLogger(__name__)


def retry_with_backoff(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    exceptions: Tuple[Type[Exception], ...] = (Exception,)
):
    """
    装饰器：指数退避重试策略
    
    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
        max_delay: 最大延迟时间（秒）
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                        
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise e
                    
                    delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {delay:.2f}s: {e}")
                    await asyncio.sleep(delay)
            
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                        
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise e
                    
                    delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {delay:.2f}s: {e}")
                    import time
                    time.sleep(delay)
            
            raise last_exception
        
        # 根据函数类型返回对应的wrapper
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


@retry_with_backoff(
    max_retries=3,
    base_delay=1.0,
    exceptions=(APIClientError, ConnectionError, TimeoutError)
)
async def retry_api_call(api_func: Callable, *args, **kwargs) -> Any:
    """重试API调用的辅助函数"""
    return await api_func(*args, **kwargs)


@retry_with_backoff(
    max_retries=5,
    base_delay=2.0,
    exceptions=(WebSocketConnectionError, ConnectionError)
)
async def retry_websocket_connect(connect_func: Callable, *args, **kwargs) -> Any:
    """重试WebSocket连接的辅助函数"""
    return await connect_func(*args, **kwargs) 